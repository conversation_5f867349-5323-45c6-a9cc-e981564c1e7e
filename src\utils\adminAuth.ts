import { createClient } from '@supabase/supabase-js';
import { AdminUser, LoginCredentials, AuthState, AdminPermissions } from '../types';

// Database interface for admin users
export interface AdminUserDB {
  id: string;
  nama: string;
  username: string;
  password_hash: string;
  role: 'admin_disdik' | 'korwil' | 'smp_admin';
  permissions: AdminPermissions;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables for admin auth');
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Transform database record to frontend type
const transformDBToFrontend = (dbRecord: AdminUserDB): AdminUser => ({
  id: dbRecord.id,
  nama: dbRecord.nama,
  username: dbRecord.username,
  role: dbRecord.role,
  permissions: dbRecord.permissions,
  created_at: dbRecord.created_at,
  updated_at: dbRecord.updated_at,
});

// Simple password hashing (in production, use proper bcrypt)
const hashPassword = (password: string): string => {
  // This is a simple hash for demo purposes
  // In production, use bcrypt or similar
  return btoa(password + 'salt_key_demo');
};

const verifyPassword = (password: string, hash: string): boolean => {
  return hashPassword(password) === hash;
};

// Admin authentication operations
export const adminAuth = {
  // Login admin user
  async login(credentials: LoginCredentials): Promise<{ success: boolean; user?: AdminUser; error?: string }> {
    try {
      console.log('Attempting admin login for username:', credentials.username);

      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('username', credentials.username)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Database error during login:', error);
        return { success: false, error: 'Username tidak ditemukan atau tidak aktif' };
      }

      if (!data) {
        return { success: false, error: 'Username tidak ditemukan' };
      }

      // Verify password (simplified for demo)
      const isValidPassword = verifyPassword(credentials.password, data.password_hash);
      if (!isValidPassword) {
        return { success: false, error: 'Password salah' };
      }

      const user = transformDBToFrontend(data);
      console.log('Login successful for user:', user.nama);
      
      return { success: true, user };
    } catch (error) {
      console.error('Error during admin login:', error);
      return { success: false, error: 'Terjadi kesalahan sistem' };
    }
  },

  // Get admin user by ID
  async getAdminUser(id: string): Promise<AdminUser | null> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return null;
      }

      return transformDBToFrontend(data);
    } catch (error) {
      console.error('Error fetching admin user:', error);
      return null;
    }
  },

  // Get all admin users (for admin management)
  async getAllAdminUsers(): Promise<AdminUser[]> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching admin users:', error);
        return [];
      }

      return data ? data.map(transformDBToFrontend) : [];
    } catch (error) {
      console.error('Error fetching admin users:', error);
      return [];
    }
  },

  // Create new admin user
  async createAdminUser(userData: Omit<AdminUser, 'id' | 'created_at' | 'updated_at'> & { password: string }): Promise<{ success: boolean; user?: AdminUser; error?: string }> {
    try {
      const passwordHash = hashPassword(userData.password);

      const dbRecord = {
        nama: userData.nama,
        username: userData.username,
        password_hash: passwordHash,
        role: userData.role,
        permissions: userData.permissions,
        is_active: true,
      };

      const { data, error } = await supabase
        .from('admin_users')
        .insert([dbRecord])
        .select()
        .single();

      if (error) {
        console.error('Error creating admin user:', error);
        return { success: false, error: 'Gagal membuat user admin' };
      }

      const user = transformDBToFrontend(data);
      return { success: true, user };
    } catch (error) {
      console.error('Error creating admin user:', error);
      return { success: false, error: 'Terjadi kesalahan sistem' };
    }
  },

  // Update admin user
  async updateAdminUser(id: string, updates: Partial<AdminUser>): Promise<{ success: boolean; user?: AdminUser; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating admin user:', error);
        return { success: false, error: 'Gagal mengupdate user admin' };
      }

      const user = transformDBToFrontend(data);
      return { success: true, user };
    } catch (error) {
      console.error('Error updating admin user:', error);
      return { success: false, error: 'Terjadi kesalahan sistem' };
    }
  },

  // Check if user has permission to access leave request
  canAccessLeaveRequest(user: AdminUser, leaveRequest: any): boolean {
    // Admin Disdik can access all
    if (user.role === 'admin_disdik' && user.permissions.canAccessAll) {
      return true;
    }

    // Korwil can access TK and SD from their kecamatan
    if (user.role === 'korwil') {
      const hasKecamatanAccess = user.permissions.kecamatanAccess?.includes(leaveRequest.kecamatan);
      const hasJenjangAccess = user.permissions.jenjangAccess?.includes(leaveRequest.jenjang);
      return hasKecamatanAccess && hasJenjangAccess;
    }

    // SMP admin can access their specific schools
    if (user.role === 'smp_admin') {
      const hasSchoolAccess = user.permissions.schoolAccess?.includes(leaveRequest.unitKerja);
      const hasJenjangAccess = user.permissions.jenjangAccess?.includes(leaveRequest.jenjang);
      return hasSchoolAccess && hasJenjangAccess;
    }

    return false;
  }
};

// Local storage utilities for auth state
export const authStorage = {
  setAuthState(authState: AuthState): void {
    localStorage.setItem('admin_auth_state', JSON.stringify(authState));
  },

  getAuthState(): AuthState | null {
    try {
      const stored = localStorage.getItem('admin_auth_state');
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  },

  clearAuthState(): void {
    localStorage.removeItem('admin_auth_state');
  }
};
