import React, { useState } from 'react';
import { UserCheck, Download, Check, X, Eye } from 'lucide-react';
import { LeaveRequest } from '../types';
import StatusBadge from './StatusBadge';
import RejectionModal from './RejectionModal';
import PDFPreviewModal from './PDFPreviewModal';

interface CoordinatorPanelProps {
  leaveRequests: LeaveRequest[];
  onApprove: (id: string, role: 'coordinator' | 'admin') => void;
  onReject: (id: string, role: 'coordinator' | 'admin', reason: string) => void;
  showModal: (message: string) => void;
}

const CoordinatorPanel: React.FC<CoordinatorPanelProps> = ({
  leaveRequests,
  onApprove,
  onReject,
  showModal
}) => {
  const [rejectionModal, setRejectionModal] = useState<{
    isOpen: boolean;
    requestId: string;
  }>({ isOpen: false, requestId: '' });
  const [pdfPreview, setPdfPreview] = useState<{
    isOpen: boolean;
    fileUrl: string;
    fileName: string;
  }>({ isOpen: false, fileUrl: '', fileName: '' });

  const coordinatorRequests = leaveRequests.filter(req => req.status === 'pending');

  const handleDownload = (fileUrl: string, fileName: string) => {
    // Open file in new tab for download
    window.open(fileUrl, '_blank');
  };

  const handlePreview = (fileUrl: string, fileName: string) => {
    setPdfPreview({ isOpen: true, fileUrl, fileName });
  };

  const handleReject = (requestId: string) => {
    setRejectionModal({ isOpen: true, requestId });
  };

  const submitRejection = (reason: string) => {
    onReject(rejectionModal.requestId, 'coordinator', reason);
    setRejectionModal({ isOpen: false, requestId: '' });
  };

  return (
    <div className="space-y-6">
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <UserCheck className="w-6 h-6 text-orange-600" />
          <h3 className="text-lg font-semibold text-orange-900">Panel Admin Jenjang Pertama</h3>
        </div>
        <p className="text-orange-700 mb-4">Sebagai admin jenjang pertama (Korwil/Cam & SMP), Anda dapat:</p>
        <ul className="space-y-2 text-orange-700">
          <li className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
            <span>Melihat pengajuan cuti dari sekolah-sekolah di wilayah/jenjang Anda</span>
          </li>
          <li className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
            <span>Memberikan persetujuan jenjang pertama untuk pengajuan cuti</span>
          </li>
          <li className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
            <span>Pengajuan yang disetujui akan diteruskan ke Admin Dinas untuk persetujuan final</span>
          </li>
        </ul>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {coordinatorRequests.length === 0 ? (
          <div className="text-center py-12">
            <UserCheck className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">
              Tidak ada pengajuan cuti yang menunggu persetujuan jenjang pertama.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">No</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Nama Guru</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">NIP</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Unit Kerja</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Jenjang</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Kecamatan</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Jenis Cuti</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Tanggal Cuti</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Status</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Berkas</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {coordinatorRequests.map((request, index) => (
                  <tr key={request.id} className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{index + 1}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b font-medium">{request.nama}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.nip}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.unitKerja}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.jenjang}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.kecamatan}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.jenisCuti}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">
                      {request.tanggalMulai} s/d {request.tanggalSelesai}
                    </td>
                    <td className="px-6 py-4 border-b">
                      <StatusBadge status={request.status} />
                    </td>
                    <td className="px-6 py-4 border-b">
                      {request.files && request.files.length > 0 ? (
                        <div className="space-y-1">
                          {request.files.map((file, fileIndex) => (
                            <div key={fileIndex} className="flex space-x-1">
                              <button
                                onClick={() => handlePreview(file.url, file.name)}
                                className="inline-flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-700 text-xs rounded hover:bg-green-200 transition-colors duration-200"
                              >
                                <Eye className="w-3 h-3" />
                                <span>Preview</span>
                              </button>
                              <button
                                onClick={() => handleDownload(file.url, file.name)}
                                className="inline-flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded hover:bg-blue-200 transition-colors duration-200"
                              >
                                <Download className="w-3 h-3" />
                                <span>Unduh</span>
                              </button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">Tidak ada</span>
                      )}
                    </td>
                    <td className="px-6 py-4 border-b">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => onApprove(request.id, 'coordinator')}
                          className="inline-flex items-center space-x-1 px-3 py-2 bg-orange-500 text-white text-sm font-medium rounded-lg hover:bg-orange-600 transition-all duration-200"
                        >
                          <Check className="w-4 h-4" />
                          <span>Setujui</span>
                        </button>
                        <button
                          onClick={() => handleReject(request.id)}
                          className="inline-flex items-center space-x-1 px-3 py-2 bg-red-500 text-white text-sm font-medium rounded-lg hover:bg-red-600 transition-all duration-200"
                        >
                          <X className="w-4 h-4" />
                          <span>Tolak</span>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <RejectionModal
        isOpen={rejectionModal.isOpen}
        onClose={() => setRejectionModal({ isOpen: false, requestId: '' })}
        onSubmit={submitRejection}
      />

      <PDFPreviewModal
        isOpen={pdfPreview.isOpen}
        onClose={() => setPdfPreview({ isOpen: false, fileUrl: '', fileName: '' })}
        fileUrl={pdfPreview.fileUrl}
        fileName={pdfPreview.fileName}
      />
    </div>
  );
};

export default CoordinatorPanel;