/*
  # Create leave requests table with RLS

  1. New Tables
    - `leave_requests`
      - `id` (text, primary key)
      - `nama` (text, not null)
      - `nip` (text, not null)
      - `jabatan` (text, not null)
      - `sekolah` (text, not null)
      - `koordinator_wilayah` (text, not null)
      - `jenis_cuti` (text, not null)
      - `tanggal_mulai` (date, not null)
      - `tanggal_selesai` (date, not null)
      - `alasan_cuti` (text, not null)
      - `files` (text, nullable - JSON string)
      - `status` (text, not null, default 'pending')
      - `rejection_reason` (text, nullable, default '')
      - `submission_date` (date, not null, default CURRENT_DATE)
      - `created_at` (timestamp, default now())
      - `updated_at` (timestamp, default now())

  2. Security
    - Enable RLS on `leave_requests` table
    - Add policies for public access (since this is a demo system)

  3. Indexes
    - Add indexes for common query patterns
*/

-- Create the leave_requests table
CREATE TABLE IF NOT EXISTS leave_requests (
  id text PRIMARY KEY DEFAULT gen_random_uuid()::text,
  nama text NOT NULL,
  nip text NOT NULL,
  jabatan text NOT NULL,
  sekolah text NOT NULL,
  koordinator_wilayah text NOT NULL,
  jenis_cuti text NOT NULL,
  tanggal_mulai date NOT NULL,
  tanggal_selesai date NOT NULL,
  alasan_cuti text NOT NULL,
  files text,
  status text NOT NULL DEFAULT 'pending',
  rejection_reason text DEFAULT '',
  submission_date date NOT NULL DEFAULT CURRENT_DATE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE leave_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (demo purposes)
CREATE POLICY "Allow public read access"
  ON leave_requests
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Allow public insert access"
  ON leave_requests
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Allow public update access"
  ON leave_requests
  FOR UPDATE
  TO public
  USING (true);

CREATE POLICY "Allow public delete access"
  ON leave_requests
  FOR DELETE
  TO public
  USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_leave_requests_nip ON leave_requests(nip);
CREATE INDEX IF NOT EXISTS idx_leave_requests_status ON leave_requests(status);
CREATE INDEX IF NOT EXISTS idx_leave_requests_submission_date ON leave_requests(submission_date);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_leave_requests_updated_at
  BEFORE UPDATE ON leave_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();