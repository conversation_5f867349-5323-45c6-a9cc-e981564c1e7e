import React, { useState, useMemo } from 'react';
import {
  Shield, Download, Check, X, Eye, Search, Filter,
  Calendar, Clock, TrendingUp, Users, FileText,
  AlertCircle, CheckCircle, XCircle, Zap, BarChart3,
  RefreshCw, Settings, Bell, Activity, Link
} from 'lucide-react';
import { LeaveRequest } from '../types';
import StatusBadge from './StatusBadge';
import RejectionModal from './RejectionModal';
import PDFPreviewModal from './PDFPreviewModal';
import DocumentDownloadButton, { CompactDocumentDownloadButton, BulkDocumentDownloadButton } from './DocumentDownloadButton';
import AttachmentDownloadButton, { CompactAttachmentDownloadButton } from './AttachmentDownloadButton';
import DriveLinkModal from './DriveLinkModal';
import LeaveRequestDetailModal from './LeaveRequestDetailModal';

interface EnhancedAdminPanelProps {
  leaveRequests: LeaveRequest[];
  onApprove: (id: string, role: 'coordinator' | 'admin') => void;
  onReject: (id: string, role: 'coordinator' | 'admin', reason: string) => void;
  onUpdate: (id: string, updates: Partial<LeaveRequest>) => Promise<boolean>;
  showModal: (message: string) => void;
  userRole?: string;
  userPermissions?: any;
}

type TabType = 'dashboard' | 'approval' | 'reports' | 'analytics';
type FilterType = 'all' | 'pending' | 'approved_coordinator' | 'approved_admin' | 'rejected';

const EnhancedAdminPanel: React.FC<EnhancedAdminPanelProps> = ({
  leaveRequests,
  onApprove,
  onReject,
  onUpdate,
  showModal,
  userRole,
  userPermissions
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [selectedRequests, setSelectedRequests] = useState<string[]>([]);
  const [filterStatus, setFilterStatus] = useState<FilterType>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [rejectionModal, setRejectionModal] = useState<{
    isOpen: boolean;
    requestId: string;
  }>({ isOpen: false, requestId: '' });
  const [pdfPreview, setPdfPreview] = useState<{
    isOpen: boolean;
    fileUrl: string;
    fileName: string;
  }>({ isOpen: false, fileUrl: '', fileName: '' });
  const [driveLinkModal, setDriveLinkModal] = useState<{
    isOpen: boolean;
    request: LeaveRequest | null
  }>({ isOpen: false, request: null });
  const [detailModal, setDetailModal] = useState<{
    isOpen: boolean;
    request: LeaveRequest | null;
  }>({ isOpen: false, request: null });

  // Real-time statistics
  const stats = useMemo(() => {
    const total = leaveRequests.length;
    const pending = leaveRequests.filter(req => req.status === 'pending').length;
    const approvedCoordinator = leaveRequests.filter(req => req.status === 'approved_coordinator').length;
    const approvedAdmin = leaveRequests.filter(req => req.status === 'approved_admin').length;
    const rejected = leaveRequests.filter(req => req.status === 'rejected').length;
    
    // Calculate processing times
    const processedRequests = leaveRequests.filter(req => req.status !== 'pending');
    const avgProcessingTime = processedRequests.length > 0 
      ? processedRequests.reduce((acc, req) => {
          const submitted = new Date(req.submissionDate);
          const now = new Date();
          return acc + (now.getTime() - submitted.getTime());
        }, 0) / processedRequests.length / (1000 * 60 * 60 * 24) // days
      : 0;

    return {
      total,
      pending,
      approvedCoordinator,
      approvedAdmin,
      rejected,
      avgProcessingTime: Math.round(avgProcessingTime * 10) / 10,
      approvalRate: total > 0 ? Math.round(((approvedAdmin + approvedCoordinator) / total) * 100) : 0
    };
  }, [leaveRequests]);

  // Filtered and searched data
  const filteredData = useMemo(() => {
    let filtered = leaveRequests;

    // Filter by user permissions first
    if (userRole && userPermissions) {
      if (userRole === 'korwil') {
        // Korwil can only see requests from their kecamatan
        const allowedKecamatan = userPermissions.kecamatan || [];
        filtered = filtered.filter(req =>
          allowedKecamatan.includes(req.kecamatan)
        );
      } else if (userRole === 'smp_admin') {
        // Check if this is SKB admin or regular SMP admin
        const allowedJenjang = userPermissions.jenjangAccess || [];
        const allowedSekolah = userPermissions.schoolAccess || userPermissions.sekolah || [];

        if (allowedJenjang.includes('SKB')) {
          // SKB admin can only see SKB requests from their sekolah
          filtered = filtered.filter(req =>
            req.jenjang === 'SKB' && allowedSekolah.includes(req.unitKerja)
          );
        } else {
          // Regular SMP admin can only see SMP requests from their sekolah
          filtered = filtered.filter(req =>
            req.jenjang === 'SMP' && allowedSekolah.includes(req.unitKerja)
          );
        }
      }
      // Admin dinas can see all (no additional filtering)
    }

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(req => req.status === filterStatus);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(req => 
        req.nama.toLowerCase().includes(term) ||
        req.unitKerja.toLowerCase().includes(term) ||
        req.kecamatan.toLowerCase().includes(term) ||
        req.jenisCuti.toLowerCase().includes(term)
      );
    }

    // Filter by date range
    if (dateRange.start && dateRange.end) {
      filtered = filtered.filter(req => {
        const submissionDate = new Date(req.submissionDate);
        const start = new Date(dateRange.start);
        const end = new Date(dateRange.end);
        return submissionDate >= start && submissionDate <= end;
      });
    }

    return filtered;
  }, [leaveRequests, filterStatus, searchTerm, dateRange]);

  // Urgent notifications
  const urgentRequests = useMemo(() => {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    
    return leaveRequests.filter(req => {
      const submissionDate = new Date(req.submissionDate);
      return req.status === 'approved_coordinator' && submissionDate <= threeDaysAgo;
    });
  }, [leaveRequests]);

  // Recent activity
  const recentActivity = useMemo(() => {
    return leaveRequests
      .filter(req => req.status !== 'pending')
      .sort((a, b) => new Date(b.submissionDate).getTime() - new Date(a.submissionDate).getTime())
      .slice(0, 5);
  }, [leaveRequests]);

  const handleSelectAll = () => {
    if (selectedRequests.length === filteredData.length) {
      setSelectedRequests([]);
    } else {
      setSelectedRequests(filteredData.map(req => req.id));
    }
  };

  const handleSelectRequest = (id: string) => {
    setSelectedRequests(prev => 
      prev.includes(id) 
        ? prev.filter(reqId => reqId !== id)
        : [...prev, id]
    );
  };

  const handleBulkApprove = () => {
    if (selectedRequests.length === 0) return;
    
    selectedRequests.forEach(id => {
      const request = leaveRequests.find(req => req.id === id);
      if (request && request.status === 'approved_coordinator') {
        onApprove(id, 'admin');
      }
    });
    
    setSelectedRequests([]);
    showModal(`${selectedRequests.length} pengajuan berhasil disetujui`);
  };

  const handleBulkReject = () => {
    if (selectedRequests.length === 0) return;
    
    // For bulk reject, we'll use a generic reason
    selectedRequests.forEach(id => {
      const request = leaveRequests.find(req => req.id === id);
      if (request && request.status === 'approved_coordinator') {
        onReject(id, 'admin', 'Ditolak melalui bulk action');
      }
    });
    
    setSelectedRequests([]);
    showModal(`${selectedRequests.length} pengajuan berhasil ditolak`);
  };

  const exportToExcel = () => {
    // Create CSV content (Excel compatible)
    const headers = [
      'No', 'Tanggal Pengajuan', 'Nama', 'Unit Kerja', 'Kecamatan', 'Jenjang',
      'Jenis Cuti', 'Tanggal Mulai', 'Tanggal Selesai', 'Status', 'Alasan Penolakan'
    ];

    const csvData = filteredData.map((request, index) => [
      index + 1,
      new Date(request.submissionDate).toLocaleDateString('id-ID'),
      request.nama,
      request.unitKerja,
      request.kecamatan,
      request.jenjang,
      request.jenisCuti,
      request.tanggalMulai,
      request.tanggalSelesai,
      request.status === 'pending' ? 'Pending' :
      request.status === 'approved_coordinator' ? 'Disetujui Koordinator' :
      request.status === 'approved_admin' ? 'Disetujui Dinas' : 'Ditolak',
      request.rejectionReason || '-'
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `laporan-cuti-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePreview = (fileUrl: string, fileName: string) => {
    setPdfPreview({ isOpen: true, fileUrl, fileName });
  };

  const handleReject = (requestId: string) => {
    setRejectionModal({ isOpen: true, requestId });
  };

  const handleRejectConfirm = (reason: string) => {
    onReject(rejectionModal.requestId, 'admin', reason);
    setRejectionModal({ isOpen: false, requestId: '' });
  };

  const handleDriveLink = (request: LeaveRequest) => {
    setDriveLinkModal({ isOpen: true, request });
  };

  const handleShowDetail = (request: LeaveRequest) => {
    setDetailModal({ isOpen: true, request });
  };

  const handleCloseDetail = () => {
    setDetailModal({ isOpen: false, request: null });
  };

  const handleDriveLinkSave = async (requestId: string, driveLink: string): Promise<boolean> => {
    try {
      console.log('Saving drive link for request:', requestId, 'Link:', driveLink);

      // Update the request with drive link
      const success = await onUpdate(requestId, { driveLink });

      if (success) {
        console.log('Drive link saved successfully');
        showModal('Link Google Drive berhasil disimpan');
        setDriveLinkModal({ isOpen: false, request: null });
        return true;
      } else {
        console.error('Update returned false');
        return false;
      }
    } catch (error) {
      console.error('Error saving drive link:', error);
      // Re-throw the error so DriveLinkModal can handle it
      throw error;
    }
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Total Pengajuan</p>
              <p className="text-3xl font-bold text-blue-900">{stats.total}</p>
              <p className="text-xs text-blue-600 mt-1">Semua waktu</p>
            </div>
            <FileText className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-700">Menunggu Approval</p>
              <p className="text-3xl font-bold text-yellow-900">{stats.approvedCoordinator}</p>
              <p className="text-xs text-yellow-600 mt-1">Perlu tindakan</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-6 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-700">Tingkat Persetujuan</p>
              <p className="text-3xl font-bold text-green-900">{stats.approvalRate}%</p>
              <p className="text-xs text-green-600 mt-1">Dari total pengajuan</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-6 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-700">Rata-rata Proses</p>
              <p className="text-3xl font-bold text-purple-900">{stats.avgProcessingTime}</p>
              <p className="text-xs text-purple-600 mt-1">Hari</p>
            </div>
            <BarChart3 className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Quick Actions & Notifications */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Urgent Notifications */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Bell className="w-5 h-5 text-red-500" />
            <h3 className="text-lg font-semibold text-gray-900">Notifikasi Urgent</h3>
            {urgentRequests.length > 0 && (
              <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {urgentRequests.length}
              </span>
            )}
          </div>
          
          {urgentRequests.length === 0 ? (
            <p className="text-gray-500 text-sm">Tidak ada pengajuan urgent</p>
          ) : (
            <div className="space-y-3">
              {urgentRequests.slice(0, 3).map(request => (
                <div key={request.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                  <div>
                    <p className="font-medium text-red-900">{request.nama}</p>
                    <p className="text-sm text-red-700">{request.unitKerja}</p>
                    <p className="text-xs text-red-600">
                      Diajukan {Math.ceil((new Date().getTime() - new Date(request.submissionDate).getTime()) / (1000 * 60 * 60 * 24))} hari lalu
                    </p>
                  </div>
                  <AlertCircle className="w-5 h-5 text-red-500" />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Activity className="w-5 h-5 text-blue-500" />
            <h3 className="text-lg font-semibold text-gray-900">Aktivitas Terbaru</h3>
          </div>
          
          <div className="space-y-3">
            {recentActivity.map(request => (
              <div key={request.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {request.status === 'approved_admin' ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : request.status === 'rejected' ? (
                    <XCircle className="w-5 h-5 text-red-500" />
                  ) : (
                    <Clock className="w-5 h-5 text-yellow-500" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{request.nama}</p>
                  <p className="text-xs text-gray-500">{request.unitKerja}</p>
                </div>
                <div className="text-xs text-gray-400">
                  {new Date(request.submissionDate).toLocaleDateString('id-ID')}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Document Downloads */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <FileText className="w-5 h-5 text-green-500" />
            <h3 className="text-lg font-semibold text-gray-900">Dokumen Siap Unduh</h3>
            {leaveRequests.filter(req => req.status === 'approved_admin').length > 0 && (
              <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {leaveRequests.filter(req => req.status === 'approved_admin').length}
              </span>
            )}
          </div>

          {leaveRequests.filter(req => req.status === 'approved_admin').length === 0 ? (
            <p className="text-gray-500 text-sm">Belum ada dokumen yang siap diunduh</p>
          ) : (
            <div className="space-y-3">
              {leaveRequests.filter(req => req.status === 'approved_admin').slice(0, 3).map(request => (
                <div key={request.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex-1">
                    <p className="font-medium text-green-900">{request.nama}</p>
                    <p className="text-sm text-green-700">{request.jenisCuti}</p>
                    <p className="text-xs text-green-600">{request.unitKerja}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <DocumentDownloadButton
                      request={request}
                      onSuccess={showModal}
                      onError={showModal}
                      variant="outline"
                      size="sm"
                    />
                    <AttachmentDownloadButton
                      request={request}
                      onSuccess={showModal}
                      onError={showModal}
                      variant="outline"
                      size="sm"
                    />
                  </div>
                </div>
              ))}
              {leaveRequests.filter(req => req.status === 'approved_admin').length > 3 && (
                <div className="text-center pt-2">
                  <BulkDocumentDownloadButton
                    requests={leaveRequests.filter(req => req.status === 'approved_admin')}
                    onSuccess={showModal}
                    onError={showModal}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderApprovalTab = () => (
    <div className="space-y-6">
      {/* Advanced Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Filter className="w-5 h-5 text-gray-600" />
          <h4 className="text-lg font-semibold text-gray-900">Filter & Pencarian Advanced</h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as FilterType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">Semua Status</option>
              <option value="pending">Pending</option>
              <option value="approved_coordinator">Disetujui Koordinator</option>
              <option value="approved_admin">Disetujui Dinas</option>
              <option value="rejected">Ditolak</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Pencarian</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Nama, sekolah, kecamatan..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal Mulai</label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal Akhir</label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Menampilkan {filteredData.length} dari {leaveRequests.length} pengajuan
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={exportToExcel}
              className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200"
            >
              <Download className="w-4 h-4" />
              <span>Export Excel</span>
            </button>
            <BulkDocumentDownloadButton
              requests={filteredData.filter(req => req.status === 'approved_admin')}
              onSuccess={showModal}
              onError={showModal}
            />
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedRequests.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-blue-900">
                {selectedRequests.length} pengajuan dipilih
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkApprove}
                className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200"
              >
                <Check className="w-4 h-4" />
                <span>Setujui Semua</span>
              </button>
              <button
                onClick={handleBulkReject}
                className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
              >
                <X className="w-4 h-4" />
                <span>Tolak Semua</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {filteredData.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">Tidak ada data sesuai filter</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedRequests.length === filteredData.length}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">No</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Tanggal</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Nama</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Unit Kerja</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Jenis Cuti</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Periode</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Status</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredData.map((request, index) => (
                  <tr key={request.id} className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedRequests.includes(request.id)}
                        onChange={() => handleSelectRequest(request.id)}
                        className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">{index + 1}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {new Date(request.submissionDate).toLocaleDateString('id-ID')}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 font-medium">{request.nama}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{request.unitKerja}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{request.jenisCuti}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {request.tanggalMulai} s/d {request.tanggalSelesai}
                    </td>
                    <td className="px-4 py-3">
                      <StatusBadge status={request.status} />
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleShowDetail(request)}
                          className="p-1 text-blue-600 hover:text-blue-800 transition-colors duration-200"
                          title="Detail Pengajuan"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        {request.status === 'approved_admin' && (
                          <CompactDocumentDownloadButton
                            request={request}
                            onSuccess={showModal}
                            onError={showModal}
                          />
                        )}
                        <CompactAttachmentDownloadButton
                          request={request}
                          onSuccess={showModal}
                          onError={showModal}
                        />
                        {request.status === 'approved_coordinator' && (
                          <>
                            <button
                              onClick={() => onApprove(request.id, 'admin')}
                              className="p-1 text-green-600 hover:text-green-800 transition-colors duration-200"
                              title="Setujui"
                            >
                              <Check className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleReject(request.id)}
                              className="p-1 text-red-600 hover:text-red-800 transition-colors duration-200"
                              title="Tolak"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </>
                        )}
                        {request.status === 'approved_admin' && (
                          <button
                            onClick={() => handleDriveLink(request)}
                            className="p-1 text-blue-600 hover:text-blue-800 transition-colors duration-200"
                            title={request.driveLink ? "Edit Link Drive" : "Tambah Link Drive"}
                          >
                            <Link className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );

  const renderReportsTab = () => (
    <div className="space-y-6">
      {/* Report Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <FileText className="w-5 h-5 text-gray-600" />
            <h4 className="text-lg font-semibold text-gray-900">Laporan & Analisis Data</h4>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={exportToExcel}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
            >
              <Download className="w-4 h-4" />
              <span>Export Laporan</span>
            </button>
            <button className="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h5 className="font-semibold text-blue-900 mb-2">Laporan Harian</h5>
            <p className="text-sm text-blue-700">Pengajuan hari ini: {leaveRequests.filter(req => {
              const today = new Date().toDateString();
              return new Date(req.submissionDate).toDateString() === today;
            }).length}</p>
          </div>

          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <h5 className="font-semibold text-green-900 mb-2">Laporan Mingguan</h5>
            <p className="text-sm text-green-700">Pengajuan minggu ini: {leaveRequests.filter(req => {
              const weekAgo = new Date();
              weekAgo.setDate(weekAgo.getDate() - 7);
              return new Date(req.submissionDate) >= weekAgo;
            }).length}</p>
          </div>

          <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <h5 className="font-semibold text-purple-900 mb-2">Laporan Bulanan</h5>
            <p className="text-sm text-purple-700">Pengajuan bulan ini: {leaveRequests.filter(req => {
              const thisMonth = new Date().getMonth();
              const thisYear = new Date().getFullYear();
              const reqDate = new Date(req.submissionDate);
              return reqDate.getMonth() === thisMonth && reqDate.getFullYear() === thisYear;
            }).length}</p>
          </div>
        </div>
      </div>

      {/* Processing Time Analysis */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Analisis Waktu Pemrosesan</h4>

        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Nama</th>
                <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Unit Kerja</th>
                <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Tanggal Pengajuan</th>
                <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Tanggal Persetujuan Korwil</th>
                <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Tanggal Persetujuan Dinas</th>
                <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Total Waktu</th>
                <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Status</th>
              </tr>
            </thead>
            <tbody>
              {leaveRequests.filter(req => req.status !== 'pending').slice(0, 10).map((request) => {
                const submissionDate = new Date(request.submissionDate);
                const now = new Date();
                const totalDays = Math.ceil((now.getTime() - submissionDate.getTime()) / (1000 * 60 * 60 * 24));

                return (
                  <tr key={request.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900 font-medium">{request.nama}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{request.unitKerja}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {submissionDate.toLocaleDateString('id-ID')}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {request.status !== 'pending' ? submissionDate.toLocaleDateString('id-ID') : '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {request.status === 'approved_admin' ? now.toLocaleDateString('id-ID') : '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        totalDays <= 3 ? 'bg-green-100 text-green-800' :
                        totalDays <= 7 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {totalDays} hari
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <StatusBadge status={request.status} />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Statistik per Jenjang</h4>
          <div className="space-y-3">
            {['TK', 'SD', 'SMP', 'SKB'].map(jenjang => {
              const count = leaveRequests.filter(req => req.jenjang === jenjang).length;
              const percentage = leaveRequests.length > 0 ? (count / leaveRequests.length * 100).toFixed(1) : 0;

              return (
                <div key={jenjang} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{jenjang}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600">{count} ({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Statistik per Jenis Cuti</h4>
          <div className="space-y-3">
            {['Cuti Tahunan', 'Cuti Sakit', 'Cuti Melahirkan', 'Cuti Besar'].map(jenis => {
              const count = leaveRequests.filter(req => req.jenisCuti === jenis).length;
              const percentage = leaveRequests.length > 0 ? (count / leaveRequests.length * 100).toFixed(1) : 0;

              return (
                <div key={jenis} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{jenis}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600">{count} ({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div className="space-y-6">
      {/* Analytics Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <TrendingUp className="w-5 h-5 text-gray-600" />
          <h4 className="text-lg font-semibold text-gray-900">Analitik & Insights</h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Pengajuan</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending + stats.approvedCoordinator}</div>
            <div className="text-sm text-gray-600">Dalam Proses</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.approvedAdmin}</div>
            <div className="text-sm text-gray-600">Disetujui</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <div className="text-sm text-gray-600">Ditolak</div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Metrik Performa</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Tingkat Persetujuan</span>
              <span className="text-lg font-bold text-green-600">{stats.approvalRate}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Rata-rata Waktu Proses</span>
              <span className="text-lg font-bold text-blue-600">{stats.avgProcessingTime} hari</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Pengajuan Urgent</span>
              <span className="text-lg font-bold text-red-600">{urgentRequests.length}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Tren Bulanan</h4>
          <div className="space-y-3">
            {Array.from({ length: 6 }, (_, i) => {
              const date = new Date();
              date.setMonth(date.getMonth() - i);
              const monthName = date.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' });
              const count = leaveRequests.filter(req => {
                const reqDate = new Date(req.submissionDate);
                return reqDate.getMonth() === date.getMonth() && reqDate.getFullYear() === date.getFullYear();
              }).length;

              return (
                <div key={i} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{monthName}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full"
                        style={{ width: `${Math.min(count * 10, 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Detailed Analytics */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Analisis Mendalam</h4>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h5 className="font-medium text-gray-900 mb-3">Top 5 Unit Kerja</h5>
            <div className="space-y-2">
              {Object.entries(
                leaveRequests.reduce((acc, req) => {
                  acc[req.unitKerja] = (acc[req.unitKerja] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)
              )
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([unit, count]) => (
                  <div key={unit} className="flex items-center justify-between text-sm">
                    <span className="text-gray-700 truncate">{unit}</span>
                    <span className="font-medium text-gray-900">{count}</span>
                  </div>
                ))}
            </div>
          </div>

          <div>
            <h5 className="font-medium text-gray-900 mb-3">Top 5 Kecamatan</h5>
            <div className="space-y-2">
              {Object.entries(
                leaveRequests.reduce((acc, req) => {
                  acc[req.kecamatan] = (acc[req.kecamatan] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)
              )
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([kecamatan, count]) => (
                  <div key={kecamatan} className="flex items-center justify-between text-sm">
                    <span className="text-gray-700">{kecamatan}</span>
                    <span className="font-medium text-gray-900">{count}</span>
                  </div>
                ))}
            </div>
          </div>

          <div>
            <h5 className="font-medium text-gray-900 mb-3">Waktu Respons</h5>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-700">≤ 3 hari</span>
                <span className="font-medium text-green-600">
                  {leaveRequests.filter(req => {
                    if (req.status === 'pending') return false;
                    const days = Math.ceil((new Date().getTime() - new Date(req.submissionDate).getTime()) / (1000 * 60 * 60 * 24));
                    return days <= 3;
                  }).length}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-700">4-7 hari</span>
                <span className="font-medium text-yellow-600">
                  {leaveRequests.filter(req => {
                    if (req.status === 'pending') return false;
                    const days = Math.ceil((new Date().getTime() - new Date(req.submissionDate).getTime()) / (1000 * 60 * 60 * 24));
                    return days > 3 && days <= 7;
                  }).length}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-700">&gt; 7 hari</span>
                <span className="font-medium text-red-600">
                  {leaveRequests.filter(req => {
                    if (req.status === 'pending') return false;
                    const days = Math.ceil((new Date().getTime() - new Date(req.submissionDate).getTime()) / (1000 * 60 * 60 * 24));
                    return days > 7;
                  }).length}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Shield className="w-6 h-6 text-purple-600" />
          <h3 className="text-lg font-semibold text-purple-900">Panel Admin Dinas Pendidikan</h3>
        </div>
        <p className="text-purple-700">
          Dashboard komprehensif untuk manajemen dan analisis pengajuan cuti ASN
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200 p-1">
        <div className="flex space-x-1">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
            { id: 'approval', label: 'Persetujuan', icon: Check },
            { id: 'reports', label: 'Laporan', icon: FileText },
            { id: 'analytics', label: 'Analitik', icon: TrendingUp }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-purple-500 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'dashboard' && renderDashboard()}
      {activeTab === 'approval' && renderApprovalTab()}
      {activeTab === 'reports' && renderReportsTab()}
      {activeTab === 'analytics' && renderAnalyticsTab()}
      
      {/* Modals */}
      <RejectionModal
        isOpen={rejectionModal.isOpen}
        onClose={() => setRejectionModal({ isOpen: false, requestId: '' })}
        onSubmit={handleRejectConfirm}
      />

      {pdfPreview.isOpen && (
        <PDFPreviewModal
          isOpen={pdfPreview.isOpen}
          fileUrl={pdfPreview.fileUrl}
          fileName={pdfPreview.fileName}
          onClose={() => setPdfPreview({ isOpen: false, fileUrl: '', fileName: '' })}
        />
      )}

      {/* Drive Link Modal */}
      {driveLinkModal.request && (
        <DriveLinkModal
          isOpen={driveLinkModal.isOpen}
          request={driveLinkModal.request}
          onClose={() => setDriveLinkModal({ isOpen: false, request: null })}
          onSave={handleDriveLinkSave}
        />
      )}

      {/* Detail Modal */}
      <LeaveRequestDetailModal
        isOpen={detailModal.isOpen}
        request={detailModal.request}
        onClose={handleCloseDetail}
        existingRequests={leaveRequests}
      />
    </div>
  );
};

export default EnhancedAdminPanel;
