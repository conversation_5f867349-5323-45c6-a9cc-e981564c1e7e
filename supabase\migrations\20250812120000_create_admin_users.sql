/*
  # Create admin users table with role-based access control

  1. New Tables
    - `admin_users`
      - `id` (text, primary key)
      - `nama` (text, not null) - Admin name
      - `nip` (text, unique, not null) - Employee ID for login
      - `email` (text, unique, not null) - Email address
      - `password_hash` (text, not null) - Hashed password
      - `role` (text, not null) - admin_disdik, korwil, smp_admin
      - `permissions` (jsonb, not null) - Role-specific permissions
      - `is_active` (boolean, default true) - Account status
      - `created_at` (timestamp, default now)
      - `updated_at` (timestamp, default now)

  2. Security
    - Enable RLS on `admin_users` table
    - Add policies for authenticated access

  3. Indexes
    - Add indexes for common query patterns

  4. Sample Data
    - Insert sample admin users for testing
*/

-- Create the admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
  id text PRIMARY KEY DEFAULT gen_random_uuid()::text,
  nama text NOT NULL,
  nip text UNIQUE NOT NULL,
  email text UNIQUE NOT NULL,
  password_hash text NOT NULL,
  role text NOT NULL CHECK (role IN ('admin_disdik', 'korwil', 'smp_admin')),
  permissions jsonb NOT NULL DEFAULT '{}',
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access
CREATE POLICY "Allow authenticated read access"
  ON admin_users
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Allow authenticated insert access"
  ON admin_users
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Allow authenticated update access"
  ON admin_users
  FOR UPDATE
  TO public
  USING (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_admin_users_nip ON admin_users(nip);
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_admin_users_updated_at 
    BEFORE UPDATE ON admin_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample admin users (passwords are hashed version of "password123")
-- In production, use proper password hashing
INSERT INTO admin_users (nama, nip, email, password_hash, role, permissions) VALUES
-- Admin Disdik (highest level)
('Admin Dinas Pendidikan', '199001012020011001', '<EMAIL>', '$2b$10$rQZ8kHWKQYQJQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'admin_disdik', '{"canAccessAll": true}'),

-- Korwil admins for different kecamatan
('Korwil Kecamatan Sukabumi', '199002012020011002', '<EMAIL>', '$2b$10$rQZ8kHWKQYQJQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'korwil', '{"kecamatanAccess": ["Sukabumi"], "jenjangAccess": ["TK", "SD"]}'),
('Korwil Kecamatan Bogor', '199003012020011003', '<EMAIL>', '$2b$10$rQZ8kHWKQYQJQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'korwil', '{"kecamatanAccess": ["Bogor"], "jenjangAccess": ["TK", "SD"]}'),
('Korwil Kecamatan Depok', '199004012020011004', '<EMAIL>', '$2b$10$rQZ8kHWKQYQJQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'korwil', '{"kecamatanAccess": ["Depok"], "jenjangAccess": ["TK", "SD"]}'),

-- SMP admins for specific schools
('Admin SMP Negeri 1', '199005012020011005', '<EMAIL>', '$2b$10$rQZ8kHWKQYQJQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'smp_admin', '{"schoolAccess": ["SMP Negeri 1"], "jenjangAccess": ["SMP"]}'),
('Admin SMP Negeri 2', '199006012020011006', '<EMAIL>', '$2b$10$rQZ8kHWKQYQJQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'smp_admin', '{"schoolAccess": ["SMP Negeri 2"], "jenjangAccess": ["SMP"]}'),
('Admin SKB Sukabumi', '199007012020011007', '<EMAIL>', '$2b$10$rQZ8kHWKQYQJQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQXQX', 'smp_admin', '{"schoolAccess": ["SKB Sukabumi"], "jenjangAccess": ["SKB"]}')

ON CONFLICT (nip) DO NOTHING;
