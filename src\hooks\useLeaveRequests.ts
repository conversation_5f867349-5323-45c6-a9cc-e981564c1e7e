import { useState, useEffect } from 'react';
import { LeaveRequest } from '../types';
import { db, LeaveRequestDB } from '../utils/database';

// Transform database record to frontend type
const transformDBToFrontend = (dbRecord: LeaveRequestDB): LeaveRequest => ({
  id: dbRecord.id,
  nama: dbRecord.nama,
  nip: dbRecord.nip,
  pangkatGolongan: dbRecord.pangkat_golongan,
  jabatan: dbRecord.jabatan,
  kecamatan: dbRecord.koordinator_wilayah,
  jenjang: dbRecord.jenjang || '',
  unitKerja: dbRecord.sekolah,
  jenisCuti: dbRecord.jenis_cuti,
  tanggalMulai: dbRecord.tanggal_mulai,
  tanggalSelesai: dbRecord.tanggal_selesai,
  alasanCuti: dbRecord.alasan_cuti,
  files: dbRecord.files ? JSON.parse(dbRecord.files) : [],
  status: dbRecord.status,
  rejectionReason: dbRecord.rejection_reason || '',
  submissionDate: dbRecord.submission_date,
});

// Transform frontend type to database record
const transformFrontendToDB = (frontendRecord: Omit<LeaveRequest, 'id' | 'submissionDate'>): Omit<LeaveRequestDB, 'id' | 'created_at' | 'updated_at'> => ({
  nama: frontendRecord.nama,
  nip: frontendRecord.nip,
  pangkat_golongan: frontendRecord.pangkatGolongan,
  jabatan: frontendRecord.jabatan,
  koordinator_wilayah: frontendRecord.kecamatan,
  sekolah: frontendRecord.sekolah,
  jenjang: frontendRecord.jenjang,
  sekolah: frontendRecord.unitKerja,
  jenis_cuti: frontendRecord.jenisCuti,
  tanggal_mulai: frontendRecord.tanggalMulai,
  tanggal_selesai: frontendRecord.tanggalSelesai,
  alasan_cuti: frontendRecord.alasanCuti,
  files: JSON.stringify(frontendRecord.files || []),
  status: frontendRecord.status,
  rejection_reason: frontendRecord.rejectionReason || '',
  submission_date: new Date().toISOString().split('T')[0],
});

export const useLeaveRequests = () => {
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load all leave requests
  const loadLeaveRequests = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Loading leave requests...');
      const dbRecords = await db.getAllLeaveRequests();
      console.log('Loaded records:', dbRecords);
      const transformedRecords = dbRecords.map(transformDBToFrontend);
      setLeaveRequests(transformedRecords);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load leave requests';
      setError(errorMessage);
      console.error('Error loading leave requests:', err);
    } finally {
      setLoading(false);
    }
  };

  // Create new leave request
  const createLeaveRequest = async (request: Omit<LeaveRequest, 'id' | 'status' | 'rejectionReason' | 'submissionDate'>) => {
    try {
      setError(null);
      console.log('Creating leave request:', request);
      
      const dbRecord = transformFrontendToDB({
        ...request,
        status: 'pending',
        rejectionReason: '',
      });
      
      console.log('DB record to create:', dbRecord);
      const createdRecord = await db.createLeaveRequest(dbRecord);
      console.log('Created record:', createdRecord);
      
      if (createdRecord) {
        const transformedRecord = transformDBToFrontend(createdRecord);
        setLeaveRequests(prev => [...prev, transformedRecord]);
        return transformedRecord;
      }
      throw new Error('Failed to create leave request');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create leave request';
      setError(errorMessage);
      console.error('Error creating leave request:', err);
      return null;
    }
  };

  // Update leave request
  const updateLeaveRequest = async (id: string, updates: Partial<LeaveRequest>) => {
    try {
      setError(null);
      console.log('Updating leave request:', id, updates);
      
      const dbUpdates: Partial<LeaveRequestDB> = {};
      
      if (updates.nama) dbUpdates.nama = updates.nama;
      if (updates.nip) dbUpdates.nip = updates.nip;
      if (updates.pangkatGolongan) dbUpdates.pangkat_golongan = updates.pangkatGolongan;
      if (updates.jabatan) dbUpdates.jabatan = updates.jabatan;
      if (updates.kecamatan) dbUpdates.koordinator_wilayah = updates.kecamatan;
      if (updates.jenjang) dbUpdates.jenjang = updates.jenjang;
      if (updates.unitKerja) dbUpdates.sekolah = updates.unitKerja;
      if (updates.jenisCuti) dbUpdates.jenis_cuti = updates.jenisCuti;
      if (updates.tanggalMulai) dbUpdates.tanggal_mulai = updates.tanggalMulai;
      if (updates.tanggalSelesai) dbUpdates.tanggal_selesai = updates.tanggalSelesai;
      if (updates.alasanCuti) dbUpdates.alasan_cuti = updates.alasanCuti;
      if (updates.files) dbUpdates.files = JSON.stringify(updates.files);
      if (updates.status) dbUpdates.status = updates.status;
      if (updates.rejectionReason !== undefined) dbUpdates.rejection_reason = updates.rejectionReason;

      const updatedRecord = await db.updateLeaveRequest(id, dbUpdates);
      if (updatedRecord) {
        const transformedRecord = transformDBToFrontend(updatedRecord);
        setLeaveRequests(prev => 
          prev.map(req => req.id === id ? transformedRecord : req)
        );
        return transformedRecord;
      }
      throw new Error('Failed to update leave request');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update leave request';
      setError(errorMessage);
      console.error('Error updating leave request:', err);
      return null;
    }
  };

  // Get leave requests by NIP
  const getLeaveRequestsByNIP = async (nip: string): Promise<LeaveRequest[]> => {
    try {
      setError(null);
      console.log('Fetching leave requests by NIP:', nip);
      const dbRecords = await db.getLeaveRequestsByNIP(nip);
      console.log('Found records for NIP:', dbRecords);
      return dbRecords.map(transformDBToFrontend);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch leave requests by NIP';
      setError(errorMessage);
      console.error('Error fetching leave requests by NIP:', err);
      return [];
    }
  };

  // Delete leave request
  const deleteLeaveRequest = async (id: string) => {
    try {
      setError(null);
      const success = await db.deleteLeaveRequest(id);
      if (success) {
        setLeaveRequests(prev => prev.filter(req => req.id !== id));
        return true;
      }
      throw new Error('Failed to delete leave request');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete leave request';
      setError(errorMessage);
      console.error('Error deleting leave request:', err);
      return false;
    }
  };

  // Load data on mount
  useEffect(() => {
    loadLeaveRequests();
  }, []);

  return {
    leaveRequests,
    loading,
    error,
    loadLeaveRequests,
    createLeaveRequest,
    updateLeaveRequest,
    getLeaveRequestsByNIP,
    deleteLeaveRequest,
  };
};