# 🔧 R2 Upload Troubleshooting Guide - Si CERDAS

## 📋 **Overview**
Comprehensive troubleshooting guide untuk mengatasi masalah upload file ke Cloudflare R2 Object Storage.

## 🚨 **Common Upload Issues & Solutions**

### **1. 🔑 Environment Variables Issues**

#### **Problem:** Environment variables tidak ter-load di browser
```
❌ Error: "R2 configuration is invalid"
❌ Console: "Missing variables: VITE_CLOUDFLARE_..."
```

#### **Solution:**
1. **Check Environment Variables:**
   - Navigate to **"Env Check"** di header
   - Verify semua required variables present (green checkmarks)
   - Missing variables akan ditampilkan dengan red X

2. **Fix Missing Variables:**
   ```env
   # Create/update .env file in project root
   VITE_CLOUDFLARE_ACCOUNT_ID=c7fc42e660d9fb84cb1dc0af35d8e97b
   VITE_CLOUDFLARE_R2_ACCESS_KEY_ID=8a7d133b87b430eb3523e50e7ea77daa
   VITE_CLOUDFLARE_R2_SECRET_ACCESS_KEY=8a3081db4770bb3ba5b09dc811e7d67003fb647a1fd48d323c0a0fb245991cb1
   VITE_CLOUDFLARE_R2_BUCKET_NAME=documents
   VITE_CLOUDFLARE_R2_ENDPOINT=https://c7fc42e660d9fb84cb1dc0af35d8e97b.r2.cloudflarestorage.com
   ```

3. **Restart Development Server:**
   ```bash
   npm run dev
   ```

### **2. 🪣 Bucket Not Found Issues**

#### **Problem:** Bucket "documents" tidak exists
```
❌ Error: "NoSuchBucket: The specified bucket does not exist"
❌ Console: "Upload test failed: NoSuchBucket"
```

#### **Solution:**
1. **Create Bucket in Cloudflare:**
   - Login to [Cloudflare Dashboard](https://dash.cloudflare.com)
   - Go to **R2 Object Storage**
   - Click **"Create bucket"**
   - Name: **"documents"**
   - Region: **Automatic**
   - Click **"Create bucket"**

2. **Verify Bucket Creation:**
   - Navigate to **"R2 Test"** panel
   - Click **"Test Connection"**
   - Should show **"Connected Successfully"**

### **3. 🌐 CORS Policy Issues**

#### **Problem:** Browser blocks cross-origin requests
```
❌ Error: "Access to fetch at '...' from origin '...' has been blocked by CORS policy"
❌ Console: "CORS error" or "Network error"
```

#### **Solution:**
1. **Configure CORS in R2 Bucket:**
   - Go to Cloudflare Dashboard → R2 Object Storage
   - Select **"documents"** bucket
   - Go to **Settings** → **CORS policy**
   - Click **"Add CORS policy"**

2. **Add CORS Configuration:**
   ```json
   [
     {
       "AllowedOrigins": ["*"],
       "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
       "AllowedHeaders": ["*"],
       "ExposeHeaders": ["ETag"],
       "MaxAgeSeconds": 3600
     }
   ]
   ```

3. **Save and Test:**
   - Click **"Save"**
   - Wait 1-2 minutes for propagation
   - Test upload again

### **4. 🔐 Credential Issues**

#### **Problem:** Invalid or expired credentials
```
❌ Error: "SignatureDoesNotMatch" or "InvalidAccessKeyId"
❌ Console: "Credential error" or "Access denied"
```

#### **Solution:**
1. **Verify Credentials in Cloudflare:**
   - Go to Cloudflare Dashboard → R2 Object Storage
   - Click **"Manage R2 API tokens"**
   - Verify your token is active and has correct permissions

2. **Check Token Permissions:**
   - **Account**: Your account
   - **Zone Resources**: Include All zones
   - **Account Resources**: Include All accounts
   - **Permissions**: 
     - Account: Cloudflare R2:Edit
     - Zone: Zone:Read

3. **Regenerate if Needed:**
   - Create new API token if current one is invalid
   - Update environment variables with new credentials
   - Restart development server

### **5. 📁 File Validation Issues**

#### **Problem:** File type or size validation fails
```
❌ Error: "Only PDF files are allowed"
❌ Error: "File size exceeds 5MB limit"
```

#### **Solution:**
1. **Check File Requirements:**
   - **Type**: Only PDF files (.pdf)
   - **Size**: Maximum 5MB
   - **Quantity**: Only 1 file at a time

2. **File Validation:**
   - Use PDF files only
   - Compress large PDFs if needed
   - Remove existing file before uploading new one

### **6. 🌐 Network/Firewall Issues**

#### **Problem:** Network blocks requests to R2
```
❌ Error: "Network error" or "Failed to fetch"
❌ Console: "TypeError: Failed to fetch"
```

#### **Solution:**
1. **Check Network Connection:**
   - Verify internet connection
   - Try accessing R2 endpoint directly in browser
   - Check if corporate firewall blocks Cloudflare

2. **Test Direct Access:**
   ```
   https://c7fc42e660d9fb84cb1dc0af35d8e97b.r2.cloudflarestorage.com
   ```

3. **Firewall Configuration:**
   - Whitelist `*.r2.cloudflarestorage.com`
   - Allow HTTPS traffic to Cloudflare IPs

## 🧪 **Debugging Steps**

### **Step 1: Environment Check**
1. Navigate to **"Env Check"** in header
2. Verify all variables are present (green checkmarks)
3. Fix any missing variables
4. Restart development server

### **Step 2: R2 Connection Test**
1. Navigate to **"R2 Test"** in header
2. Check configuration status (should be green)
3. Click **"Test Connection"**
4. Verify **"Connected Successfully"** message

### **Step 3: Upload Test**
1. In R2 Test panel, select a PDF file
2. Click **"Upload to R2"**
3. Check **"Show Logs"** for detailed diagnostics
4. Verify successful upload with URL

### **Step 4: Browser Console Check**
1. Open browser Developer Tools (F12)
2. Go to **Console** tab
3. Look for detailed error messages
4. Check **Network** tab for failed requests

### **Step 5: Form Integration Test**
1. Go to **"Pengajuan Cuti"** form
2. Try uploading a PDF file
3. Check for success message
4. Verify file appears in form

## 📊 **Diagnostic Logs**

### **Successful Upload Logs:**
```
🚀 Starting R2 upload process...
📁 File details: {name: "document.pdf", size: 1234567, type: "application/pdf"}
🔧 Validating R2 configuration...
✅ R2 configuration is valid
🔗 Creating R2 client...
✅ R2 client created successfully
📋 Validating file...
📏 File size: 1.18MB (limit: 5MB)
📄 File type: application/pdf
✅ File validation passed
🔑 Generated key: users/user_123/1734567890_abc123_document.pdf
🔄 Converting file to buffer...
✅ Buffer created: 1234567 bytes
☁️ Uploading to R2...
📤 Sending upload command to R2...
✅ Upload successful!
🔗 Generated URL: https://...
🎉 Upload process completed successfully
```

### **Failed Upload Logs:**
```
❌ R2 configuration is invalid
Missing variables: VITE_CLOUDFLARE_R2_BUCKET_NAME
```

## 🔧 **Quick Fixes**

### **Fix 1: Missing Environment Variables**
```bash
# Create .env file
echo "VITE_CLOUDFLARE_ACCOUNT_ID=c7fc42e660d9fb84cb1dc0af35d8e97b" > .env
echo "VITE_CLOUDFLARE_R2_ACCESS_KEY_ID=8a7d133b87b430eb3523e50e7ea77daa" >> .env
echo "VITE_CLOUDFLARE_R2_SECRET_ACCESS_KEY=8a3081db4770bb3ba5b09dc811e7d67003fb647a1fd48d323c0a0fb245991cb1" >> .env
echo "VITE_CLOUDFLARE_R2_BUCKET_NAME=documents" >> .env
echo "VITE_CLOUDFLARE_R2_ENDPOINT=https://c7fc42e660d9fb84cb1dc0af35d8e97b.r2.cloudflarestorage.com" >> .env

# Restart server
npm run dev
```

### **Fix 2: Create Bucket**
1. Go to [Cloudflare R2](https://dash.cloudflare.com/r2)
2. Click **"Create bucket"**
3. Name: **"documents"**
4. Click **"Create bucket"**

### **Fix 3: Add CORS Policy**
1. Select **"documents"** bucket
2. Go to **Settings** → **CORS policy**
3. Add the CORS configuration above
4. Save and wait 1-2 minutes

## 📞 **Getting Help**

### **If Upload Still Fails:**
1. **Check Environment Variables** - Use "Env Check" panel
2. **Test R2 Connection** - Use "R2 Test" panel with logs
3. **Verify Bucket Exists** - Check Cloudflare dashboard
4. **Configure CORS** - Add CORS policy to bucket
5. **Check Browser Console** - Look for specific error messages
6. **Test Network Access** - Try accessing R2 endpoint directly

### **Debug Information to Collect:**
- Environment variables status (from Env Check)
- R2 connection test results (from R2 Test)
- Browser console errors
- Network tab in developer tools
- File details (name, size, type)

---

## ✅ **Success Checklist**

- [ ] All environment variables present (green in Env Check)
- [ ] R2 connection test successful
- [ ] "documents" bucket exists in Cloudflare
- [ ] CORS policy configured in bucket
- [ ] Test upload successful in R2 Test panel
- [ ] Form upload working in Pengajuan Cuti
- [ ] No errors in browser console

**Once all items are checked, file upload should work perfectly!** 🚀
