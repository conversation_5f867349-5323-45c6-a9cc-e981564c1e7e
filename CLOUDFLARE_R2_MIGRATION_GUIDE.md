# 🚀 Cloudflare R2 Object Storage Migration - Si CERDAS

## 📋 **Overview**
Migrasi lengkap dari local/Supabase storage ke Cloudflare R2 Object Storage untuk file handling yang lebih cost-effective dan performant.

## 🎯 **Keuntungan Cloudflare R2**
- ✅ **Zero Egress Fees** - Tidak ada biaya download/transfer
- ✅ **S3 Compatible API** - Mudah migrasi dari S3
- ✅ **Global Performance** - CDN terintegrasi
- ✅ **Cost Effective** - Lebih murah dari AWS S3
- ✅ **Simple Setup** - Konfigurasi yang mudah

## 🔧 **Implementation Details**

### **1. 📦 Dependencies Added**
```json
{
  "dependencies": {
    "@aws-sdk/client-s3": "^3.x.x",
    "@aws-sdk/s3-request-presigner": "^3.x.x"
  }
}
```

### **2. 🔑 Environment Variables**
```env
# Cloudflare R2 Object Storage
VITE_CLOUDFLARE_ACCOUNT_ID=c7fc42e660d9fb84cb1dc0af35d8e97b
VITE_CLOUDFLARE_R2_ACCESS_KEY_ID=8a7d133b87b430eb3523e50e7ea77daa
VITE_CLOUDFLARE_R2_SECRET_ACCESS_KEY=8a3081db4770bb3ba5b09dc811e7d67003fb647a1fd48d323c0a0fb245991cb1
VITE_CLOUDFLARE_R2_BUCKET_NAME=si-cerdas-files
VITE_CLOUDFLARE_R2_ENDPOINT=https://c7fc42e660d9fb84cb1dc0af35d8e97b.r2.cloudflarestorage.com
```

### **3. 🛠️ R2 Storage Service**
**File:** `src/utils/r2Storage.ts`

#### **Key Features:**
- ✅ **S3 Compatible Client** - Using AWS SDK
- ✅ **File Upload** - With progress tracking
- ✅ **File Deletion** - Clean storage management
- ✅ **URL Generation** - Public and signed URLs
- ✅ **Validation** - Configuration and connection testing
- ✅ **Error Handling** - Comprehensive error management

#### **Core Functions:**
```typescript
// Upload file to R2
uploadFileToR2(file: File, userId?: string, onProgress?: (progress: number) => void)

// Delete file from R2
deleteFileFromR2(key: string): Promise<boolean>

// Generate signed URL (for private access)
generateSignedUrl(key: string, expiresIn?: number): Promise<string | null>

// Get public URL
getPublicUrl(key: string): string

// Test R2 connection
testR2Connection(): Promise<boolean>

// Validate configuration
validateR2Config(): boolean
```

### **4. 📁 File Organization Structure**
```
R2 Bucket: si-cerdas-files/
├── users/
│   ├── user_198810052020121006/
│   │   ├── 1734567890_abc123_document.pdf
│   │   └── 1734567891_def456_certificate.pdf
│   └── user_199001012021011001/
│       └── 1734567892_ghi789_leave_request.pdf
├── uploads/
│   ├── 1734567893_jkl012_anonymous_file.pdf
│   └── 1734567894_mno345_temp_upload.pdf
└── test/
    └── connection-test-files
```

### **5. 🔄 Updated Components**

#### **LeaveForm.tsx Updates:**
- ✅ **R2 Upload Integration** - Replace Supabase storage
- ✅ **Progress Tracking** - Real-time upload progress
- ✅ **Enhanced UI** - R2 branding and status indicators
- ✅ **Error Handling** - Better error messages
- ✅ **File Management** - R2 key tracking for deletion

#### **Key Changes:**
```typescript
// Before (Supabase)
const result = await uploadFile(file, 'leave-documents');

// After (R2)
const result = await uploadFileToR2(file, userId, (progress) => {
  setUploadProgress(prev => ({ ...prev, [file.name]: progress }));
});
```

#### **UI Enhancements:**
- 🔄 **Upload Progress** - Spinner and progress indicators
- 📁 **R2 Branding** - "Powered by Cloudflare R2 Storage"
- ✅ **Success States** - Green indicators for successful uploads
- 🗑️ **Delete Integration** - R2 key-based file deletion

### **6. 🧪 R2 Test Panel**
**File:** `src/components/R2TestPanel.tsx`

#### **Features:**
- ✅ **Configuration Validation** - Check all environment variables
- ✅ **Connection Testing** - Test R2 connectivity
- ✅ **File Upload Test** - Upload test files
- ✅ **File Deletion Test** - Delete test files
- ✅ **URL Generation** - Test public URL access

#### **Access:**
- **Navigation:** Header → "R2 Test"
- **URL:** `/#r2test`

## 💰 **Cost Analysis**

### **Cloudflare R2 Pricing:**
```
Storage: $0.015/GB/month
Class A Operations: $4.50/million (PUT, COPY, POST, LIST)
Class B Operations: $0.36/million (GET, SELECT)
Egress: FREE (major advantage!)
```

### **Estimated Monthly Cost (Si CERDAS):**
```
Assumptions:
- 5GB file storage (PDF documents)
- 2000 uploads/month
- 10000 downloads/month

R2 Cost:
- Storage: 5GB × $0.015 = $0.075
- Class A: 2000 × $4.50/1M = $0.009
- Class B: 10000 × $0.36/1M = $0.0036
- Egress: $0 (FREE!)
Total: ~$0.09/month

vs AWS S3:
- Storage: 5GB × $0.023 = $0.115
- PUT: 2000 × $0.005/1K = $0.01
- GET: 10000 × $0.0004/1K = $0.004
- Egress: 5GB × $0.09 = $0.45
Total: ~$0.58/month

Savings: ~$0.49/month (84% cheaper!)
```

## 🔧 **Technical Implementation**

### **File Upload Flow:**
```
1. User selects PDF file
2. Frontend validates file (type, size)
3. Generate unique R2 key with user ID
4. Upload to R2 with progress tracking
5. Store R2 URL and key in database
6. Display success with R2 branding
```

### **File Access Flow:**
```
1. User requests file download/preview
2. Use stored R2 URL for direct access
3. Cloudflare CDN serves file globally
4. Zero egress fees for downloads
```

### **File Deletion Flow:**
```
1. User removes file from form
2. Extract R2 key from stored URL
3. Delete file from R2 storage
4. Remove database reference
5. Update UI state
```

## 🧪 **Testing Guide**

### **1. Configuration Test:**
1. **Navigate** to R2 Test panel
2. **Check** configuration status (should be green)
3. **Verify** all credentials are properly set

### **2. Connection Test:**
1. **Click** "Test Connection" button
2. **Wait** for connection result
3. **Verify** "Connected Successfully" message

### **3. Upload Test:**
1. **Select** a PDF file in test panel
2. **Click** "Upload to R2"
3. **Verify** successful upload with URL
4. **Check** file is accessible via URL

### **4. Delete Test:**
1. **Click** "Delete" button on uploaded file
2. **Verify** file is removed from R2
3. **Check** URL is no longer accessible

### **5. Form Integration Test:**
1. **Go** to "Pengajuan Cuti" form
2. **Upload** a PDF file
3. **Verify** "Powered by Cloudflare R2" message
4. **Check** green success indicator
5. **Submit** form and verify file URL in database

## 📊 **Performance Metrics**

### **Build Results:**
- ✅ **Build Successful** - No errors
- 📦 **Bundle Size** - 890.76KB (265.77KB gzipped)
- ⚡ **Build Time** - 28.66s
- 📈 **Size Increase** - +232KB (AWS SDK for R2)

### **Runtime Performance:**
- ⚡ **Upload Speed** - Direct to R2, no proxy
- 🌍 **Global CDN** - Fast access worldwide
- 📱 **Mobile Optimized** - Responsive upload UI
- 🔄 **Progress Tracking** - Real-time feedback

## 🔒 **Security Features**

### **Access Control:**
- 🔑 **API Keys** - Secure R2 access
- 👤 **User Isolation** - Files organized by user ID
- 🔒 **Type Validation** - PDF files only
- 📏 **Size Limits** - 5MB maximum
- 🕒 **Signed URLs** - Time-limited access (if needed)

### **Data Protection:**
- 🛡️ **Cloudflare Security** - Enterprise-grade protection
- 🔐 **Encrypted Storage** - Data encrypted at rest
- 🌐 **HTTPS Only** - Secure transmission
- 🗑️ **Clean Deletion** - Proper file cleanup

## 📈 **Benefits Achieved**

### **Cost Savings:**
- 💰 **84% Cost Reduction** - vs AWS S3
- 🆓 **Zero Egress Fees** - Major savings on downloads
- 📊 **Predictable Pricing** - No surprise bandwidth charges

### **Performance Improvements:**
- ⚡ **Global CDN** - Faster file access
- 🚀 **Direct Upload** - No server proxy needed
- 📱 **Better UX** - Progress tracking and feedback
- 🔄 **Reliable Storage** - Cloudflare infrastructure

### **Developer Experience:**
- 🛠️ **S3 Compatible** - Familiar API
- 🧪 **Test Panel** - Easy debugging and testing
- 📝 **Clear Documentation** - Comprehensive guides
- 🔧 **Easy Maintenance** - Simple configuration

---

## ✅ **Migration Status: COMPLETE**

**R2 Integration**: ✅ Fully implemented and tested
**File Upload**: ✅ Working with progress tracking
**File Deletion**: ✅ Proper cleanup implemented
**Test Panel**: ✅ Comprehensive testing tools
**Documentation**: ✅ Complete implementation guide

**Next Steps:**
1. **Test** R2 functionality in production
2. **Monitor** upload/download performance
3. **Verify** cost savings in Cloudflare dashboard
4. **Migrate** existing files (if any) from old storage

**Ready for Production!** 🚀
