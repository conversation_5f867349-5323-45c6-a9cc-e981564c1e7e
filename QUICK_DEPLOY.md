# 🚀 Quick Deploy ke Netlify

## ⚡ Langkah Cepat (5 Menit)

### 1. Persiapan Environment
```bash
# Copy environment variables
cp .env.example .env

# Edit .env dengan credentials Supabase Anda:
VITE_SUPABASE_URL=https://olqzomqxrnzekomszkfe.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

### 2. Build & Test
```bash
# Install dependencies
npm install

# Build untuk production
npm run build

# Test build (optional)
npm run preview
```

### 3. Deploy ke Netlify

#### Option A: Drag & Drop (Tercepat)
1. Buka [netlify.com](https://netlify.com)
2. Drag folder `dist` ke dashboard Netlify
3. Set environment variables di Site Settings
4. Done! ✅

#### Option B: GitHub Integration (Recommended)
1. Push ke GitHub:
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. Di Netlify:
   - New site from Git
   - Connect GitHub repo
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Add environment variables

## 🔧 Environment Variables untuk Netlify

Di Netlify Dashboard → Site Settings → Environment Variables:

```
VITE_SUPABASE_URL = https://olqzomqxrnzekomszkfe.supabase.co
VITE_SUPABASE_ANON_KEY = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## ✅ Checklist Deployment

- [ ] `.env` file configured
- [ ] `npm run build` successful
- [ ] `netlify.toml` exists
- [ ] Environment variables set in Netlify
- [ ] Site deployed and accessible
- [ ] Login system works
- [ ] File upload works
- [ ] Document download works

## 🌐 Expected Result

Setelah deployment, aplikasi akan tersedia di:
- **URL**: `https://your-site-name.netlify.app`
- **Features**: Semua fitur berfungsi normal
- **Performance**: Fast loading dengan CDN global
- **Security**: HTTPS enabled automatically

## 🚨 Troubleshooting

### Build Error
```bash
# Clear cache
rm -rf node_modules package-lock.json
npm install
npm run build
```

### Environment Variables Not Working
- Pastikan prefix `VITE_`
- Restart deployment setelah add env vars
- Check console untuk error messages

### 404 on Page Refresh
- Pastikan `netlify.toml` ada di root
- Check redirects configuration

## 📞 Need Help?

1. Check build logs di Netlify dashboard
2. Verify environment variables
3. Test locally dengan `npm run build && npm run preview`
4. Check browser console untuk errors

---

**Total Time**: ~5-10 menit untuk deployment pertama
**Auto-deploy**: Setiap push ke GitHub akan trigger rebuild otomatis
