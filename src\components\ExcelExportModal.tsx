import React, { useState } from 'react';
import { X, Download, Calendar, Building } from 'lucide-react';
import { LeaveRequest } from '../types';
import { exportAllLeaveRequests, exportLeaveRequestsByUnit } from '../utils/excelExport';

interface ExcelExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  leaveRequests: LeaveRequest[];
  userRole?: 'admin' | 'coordinator' | 'disdik';
  userUnitKerja?: string;
}

const ExcelExportModal: React.FC<ExcelExportModalProps> = ({
  isOpen,
  onClose,
  leaveRequests,
  userRole = 'admin',
  userUnitKerja
}) => {
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedUnit, setSelectedUnit] = useState<string>(userUnitKerja || '');
  const [isExporting, setIsExporting] = useState(false);

  if (!isOpen) return null;

  const months = [
    { value: 1, label: 'Januari' },
    { value: 2, label: 'Februari' },
    { value: 3, label: 'Maret' },
    { value: 4, label: 'April' },
    { value: 5, label: 'Mei' },
    { value: 6, label: 'Juni' },
    { value: 7, label: 'Juli' },
    { value: 8, label: 'Agustus' },
    { value: 9, label: 'September' },
    { value: 10, label: 'Oktober' },
    { value: 11, label: 'November' },
    { value: 12, label: 'Desember' }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);

  // Get unique unit kerja for dropdown (for disdik admin)
  const uniqueUnits = Array.from(new Set(leaveRequests.map(req => req.unitKerja))).sort();

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      let result;
      
      if (userRole === 'disdik' && selectedUnit) {
        // Export by specific unit
        result = exportLeaveRequestsByUnit(leaveRequests, selectedMonth, selectedYear, selectedUnit);
      } else if (userRole === 'disdik') {
        // Export all requests (disdik admin)
        result = exportAllLeaveRequests(leaveRequests, selectedMonth, selectedYear);
      } else {
        // Export for specific unit (coordinator/admin)
        result = exportLeaveRequestsByUnit(leaveRequests, selectedMonth, selectedYear, userUnitKerja || '');
      }

      if (result.success) {
        alert(`✅ ${result.message}\nFile: ${result.filename}\nTotal: ${result.totalRecords} record`);
        onClose();
      }
    } catch (error) {
      console.error('Export error:', error);
      alert('❌ Gagal mengexport data. Silakan coba lagi.');
    } finally {
      setIsExporting(false);
    }
  };

  // Filter requests for preview count
  const getPreviewCount = () => {
    let filtered = leaveRequests.filter(request => {
      const startDate = new Date(request.tanggalMulai);
      return startDate.getMonth() + 1 === selectedMonth && startDate.getFullYear() === selectedYear;
    });

    if (userRole !== 'disdik' && userUnitKerja) {
      filtered = filtered.filter(req => req.unitKerja.toLowerCase().includes(userUnitKerja.toLowerCase()));
    } else if (userRole === 'disdik' && selectedUnit) {
      filtered = filtered.filter(req => req.unitKerja.toLowerCase().includes(selectedUnit.toLowerCase()));
    }

    return filtered.length;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Download className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Download Rekap Cuti</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          {/* Month Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              Bulan
            </label>
            <select
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {months.map(month => (
                <option key={month.value} value={month.value}>
                  {month.label}
                </option>
              ))}
            </select>
          </div>

          {/* Year Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tahun
            </label>
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {years.map(year => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>

          {/* Unit Kerja Selection (for disdik admin) */}
          {userRole === 'disdik' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Building className="w-4 h-4 inline mr-1" />
                Unit Kerja (Opsional)
              </label>
              <select
                value={selectedUnit}
                onChange={(e) => setSelectedUnit(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Semua Unit Kerja</option>
                {uniqueUnits.map(unit => (
                  <option key={unit} value={unit}>
                    {unit}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Preview Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              <strong>Preview:</strong> {getPreviewCount()} pengajuan cuti akan diexport
            </p>
            <p className="text-xs text-blue-600 mt-1">
              Periode: {months.find(m => m.value === selectedMonth)?.label} {selectedYear}
              {userRole === 'disdik' && selectedUnit && ` - ${selectedUnit}`}
              {userRole !== 'disdik' && userUnitKerja && ` - ${userUnitKerja}`}
            </p>
          </div>

          {/* Export Info */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <p className="text-sm text-gray-700 font-medium mb-1">Format Export:</p>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• File Excel (.xlsx)</li>
              <li>• Data lengkap termasuk link dokumen</li>
              <li>• Summary statistik di akhir</li>
              <li>• Format siap cetak</li>
            </ul>
          </div>
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            disabled={isExporting}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            Batal
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting || getPreviewCount() === 0}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Mengexport...</span>
              </>
            ) : (
              <>
                <Download className="w-4 h-4" />
                <span>Download Excel</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExcelExportModal;
