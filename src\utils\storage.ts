import { supabase } from './database';
import { createClient } from '@supabase/supabase-js';

// Create a separate client with service role for storage operations
const supabaseServiceRole = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY
);

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export const uploadFile = async (file: File, folder: string = 'leave-documents'): Promise<UploadResult> => {
  try {
    console.log('Starting file upload to Supabase storage...');
    console.log('File details:', { name: file.name, size: file.size, type: file.type });
    
    // Generate unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${fileExt}`;
    const filePath = `${folder}/${fileName}`;

    console.log('Upload path:', filePath);
    console.log('Attempting to upload file:', fileName);
    console.log('File path:', filePath);
    console.log('File size:', file.size);

    // Upload file to Supabase storage using service role (bypasses RLS)
    const { data, error } = await supabaseServiceRole.storage
      .from('documents')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Upload error details:', error.message);
      console.error('Full error object:', error);
      return {
        success: false,
        error: `Upload failed: ${error.message}`
      };
    }

    console.log('Upload successful:', data);

    // Get public URL using service role
    const { data: urlData } = supabaseServiceRole.storage
      .from('documents')
      .getPublicUrl(filePath);

    console.log('Public URL generated:', urlData.publicUrl);

    return {
      success: true,
      url: urlData.publicUrl
    };

  } catch (error) {
    console.error('Error uploading file:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown upload error'
    };
  }
};

export const deleteFile = async (filePath: string): Promise<boolean> => {
  try {
    console.log('Deleting file from Supabase storage:', filePath);
    console.log('Attempting to delete file:', filePath);

    // Delete file using service role (bypasses RLS)
    const { error } = await supabaseServiceRole.storage
      .from('documents')
      .remove([filePath]);

    if (error) {
      console.error('Supabase storage delete error:', error);
      console.error('Delete error details:', error.message);
      return false;
    }

    console.log('File deleted successfully');
    console.log('File deleted successfully:', filePath);
    return true;

  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

// Extract file path from URL for deletion
export const getFilePathFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    // Remove '/storage/v1/object/public/documents/' part
    const relevantParts = pathParts.slice(6); // Adjust based on Supabase URL structure
    return relevantParts.join('/');
  } catch (error) {
    console.error('Error extracting file path from URL:', error);
    return '';
  }
};