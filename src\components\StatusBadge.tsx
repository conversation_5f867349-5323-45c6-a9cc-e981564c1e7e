import React from 'react';
import { Clock, CheckCircle, Award, XCircle } from 'lucide-react';

interface StatusBadgeProps {
  status: 'pending' | 'approved_coordinator' | 'approved_admin' | 'rejected';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          text: 'Pending',
          icon: Clock,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
      case 'approved_coordinator':
        return {
          text: 'Disetujui Koordinator',
          icon: CheckCircle,
          className: 'bg-orange-100 text-orange-800 border-orange-200'
        };
      case 'approved_admin':
        return {
          text: 'Disetuju<PERSON> Dinas',
          icon: Award,
          className: 'bg-green-100 text-green-800 border-green-200'
        };
      case 'rejected':
        return {
          text: '<PERSON>to<PERSON>',
          icon: XCircle,
          className: 'bg-red-100 text-red-800 border-red-200'
        };
      default:
        return {
          text: 'Unknown',
          icon: Clock,
          className: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium border ${config.className}`}>
      <Icon className="w-4 h-4" />
      <span>{config.text}</span>
    </span>
  );
};

export default StatusBadge;