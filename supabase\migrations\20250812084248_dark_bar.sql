/*
  # Recreate leave_requests table with updated schema

  1. New Tables
    - Drop and recreate `leave_requests` table with all required columns:
      - `id` (text, primary key)
      - `nama` (text, not null) - Name
      - `nip` (text, not null) - Employee ID
      - `pangkat_golongan` (text, not null) - Rank/Grade
      - `jabatan` (text, not null) - Position
      - `koordinator_wilayah` (text, not null) - District/Kecamatan
      - `jenjang` (text, not null) - Education level
      - `sekolah` (text, not null) - School/Unit Kerja
      - `jenis_cuti` (text, not null) - Leave type
      - `tanggal_mulai` (date, not null) - Start date
      - `tanggal_selesai` (date, not null) - End date
      - `alasan_cuti` (text, not null) - Leave reason
      - `files` (text) - File attachments as JSON string
      - `status` (text, default 'pending') - Request status
      - `rejection_reason` (text) - Rejection reason
      - `submission_date` (date, default current date) - Submission date
      - `created_at` (timestamp, default now)
      - `updated_at` (timestamp, default now)

  2. Security
    - Enable RLS on `leave_requests` table
    - Add policies for public access (as per original setup)

  3. Indexes
    - Add indexes for common query patterns
*/

-- Drop existing table if it exists
DROP TABLE IF EXISTS leave_requests CASCADE;

-- Create the leave_requests table with correct schema
CREATE TABLE leave_requests (
  id text PRIMARY KEY,
  nama text NOT NULL,
  nip text NOT NULL,
  pangkat_golongan text NOT NULL,
  jabatan text NOT NULL,
  koordinator_wilayah text NOT NULL,
  jenjang text NOT NULL,
  sekolah text NOT NULL,
  jenis_cuti text NOT NULL,
  tanggal_mulai date NOT NULL,
  tanggal_selesai date NOT NULL,
  alasan_cuti text NOT NULL,
  files text,
  status text NOT NULL DEFAULT 'pending',
  rejection_reason text DEFAULT '',
  submission_date date NOT NULL DEFAULT CURRENT_DATE,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security
ALTER TABLE leave_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (matching original setup)
CREATE POLICY "Allow public read access"
  ON leave_requests
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Allow public insert access"
  ON leave_requests
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Allow public update access"
  ON leave_requests
  FOR UPDATE
  TO public
  USING (true);

CREATE POLICY "Allow public delete access"
  ON leave_requests
  FOR DELETE
  TO public
  USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_leave_requests_nip ON leave_requests (nip);
CREATE INDEX IF NOT EXISTS idx_leave_requests_status ON leave_requests (status);
CREATE INDEX IF NOT EXISTS idx_leave_requests_submission_date ON leave_requests (submission_date);
CREATE INDEX IF NOT EXISTS idx_leave_requests_koordinator_wilayah ON leave_requests (koordinator_wilayah);
CREATE INDEX IF NOT EXISTS idx_leave_requests_jenjang ON leave_requests (jenjang);

-- Create function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_leave_requests_updated_at
    BEFORE UPDATE ON leave_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();