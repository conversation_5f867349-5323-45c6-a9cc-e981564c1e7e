# 📁📊 Link Drive & Download Rekap Excel

## 📋 **Overview**
Implementasi dua fitur baru untuk meningkatkan fleksibilitas upload dokumen dan kemudahan pelaporan:
1. **Link Google Drive** - Alternatif upload dokumen melalui link Drive (file atau folder)
2. **Download Rekap Excel** - Export data cuti dalam format Excel dengan template yang sesuai

## 🔗 **Fitur 1: Link Google Drive**

### **📁 Fleksibilitas Upload Dokumen**

**Before:**
- ❌ Hanya upload file PDF langsung ke sistem
- ❌ Terbatas ukuran file (5MB)
- ❌ Hanya 1 file per pengajuan

**After:**
- ✅ Upload file PDF langsung ke sistem
- ✅ **Link Google Drive** (file atau folder)
- ✅ Fleksibilitas lebih besar untuk dokumen pendukung
- ✅ Dapat berbagi folder dengan multiple dokumen

### **🎨 UI Enhancement**

#### **Info Panel Baru:**
```
ℹ️ Pilihan Upload Dokumen:
• Upload File: Upload file PDF langsung ke sistem
• Link Drive: Berikan link Google Drive (file atau folder)
```

#### **Field Link Drive:**
```typescript
<input
  type="url"
  name="driveLink"
  value={formData.driveLink || ''}
  placeholder="https://drive.google.com/... (file atau folder)"
/>
<p className="text-xs text-gray-500 mt-1">
  Alternatif upload: berikan link Google Drive yang dapat diakses oleh admin
</p>
```

### **📊 Database Schema Update**

#### **LeaveRequest Interface:**
```typescript
export interface LeaveRequest {
  // ... existing fields
  driveLink?: string; // Google Drive link for documents
}
```

### **🎯 Use Cases**

#### **Scenario 1: File Besar**
```
User memiliki file PDF > 5MB
→ Upload ke Google Drive
→ Share link dengan akses "Anyone with link can view"
→ Paste link di field "Link Google Drive"
```

#### **Scenario 2: Multiple Dokumen**
```
User memiliki beberapa dokumen pendukung
→ Buat folder di Google Drive
→ Upload semua dokumen ke folder
→ Share folder dengan akses publik
→ Paste link folder di field "Link Google Drive"
```

#### **Scenario 3: Dokumen Sudah di Drive**
```
User sudah memiliki dokumen di Google Drive
→ Copy link dokumen/folder
→ Paste langsung di field "Link Google Drive"
→ Tidak perlu download-upload ulang
```

## 📊 **Fitur 2: Download Rekap Excel**

### **📈 Export Data Cuti dalam Format Excel**

#### **Template Excel yang Dihasilkan:**
```
REKAP USULAN CUTI {BULAN} {TAHUN}
UNIT KERJA: {UNIT_KERJA} (jika dipilih)

NO | NAMA | NIP | PANGKAT/GOL | JABATAN | KECAMATAN | JENJANG | UNIT KERJA | 
JENIS CUTI | TANGGAL MULAI | TANGGAL SELESAI | DURASI (HARI) | ALASAN CUTI | 
STATUS | TANGGAL PENGAJUAN | LINK DOKUMEN

TOTAL PENGAJUAN: X usulan
DISETUJUI: X usulan
DITOLAK: X usulan
PENDING: X usulan
```

### **🔧 Technical Implementation**

#### **Excel Export Utility:**
```typescript
// src/utils/excelExport.ts
export const exportLeaveRequestsToExcel = (
  requests: LeaveRequest[],
  month: number,
  year: number,
  unitKerja?: string
) => {
  // Filter by month/year
  // Create Excel workbook
  // Format data with Indonesian date format
  // Add summary statistics
  // Download file
};
```

#### **Export Modal Component:**
```typescript
// src/components/ExcelExportModal.tsx
interface ExcelExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  leaveRequests: LeaveRequest[];
  userRole?: 'admin' | 'coordinator' | 'disdik';
  userUnitKerja?: string;
}
```

### **🎯 Export Options per Role**

#### **👥 Admin Wilayah (Coordinator):**
- ✅ Export untuk unit kerja sendiri
- ✅ Filter bulan/tahun
- ✅ Data sesuai kewenangan wilayah

#### **🏫 Admin SMP (Admin):**
- ✅ Export untuk unit kerja sendiri
- ✅ Filter bulan/tahun
- ✅ Data sesuai sekolah/unit

#### **🏛️ Admin Disdik (Enhanced Admin):**
- ✅ Export semua unit kerja
- ✅ Export per unit kerja tertentu
- ✅ Filter bulan/tahun
- ✅ Data komprehensif seluruh dinas

### **📋 Export Modal Features**

#### **🗓️ Date Selection:**
```
Bulan: [Dropdown Januari-Desember]
Tahun: [Dropdown 5 tahun terakhir]
```

#### **🏢 Unit Kerja Selection (Disdik Only):**
```
Unit Kerja: [Dropdown]
- Semua Unit Kerja
- SMP Negeri 1 Grobogan
- SMP Negeri 2 Grobogan
- ... (dynamic dari data)
```

#### **📊 Preview Info:**
```
Preview: X pengajuan cuti akan diexport
Periode: Januari 2024 - SMP Negeri 1 Grobogan
```

#### **📁 Export Info:**
```
Format Export:
• File Excel (.xlsx)
• Data lengkap termasuk link dokumen
• Summary statistik di akhir
• Format siap cetak
```

### **🎨 UI Integration**

#### **AdminPanel (SMP):**
```typescript
<div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
  <h3>Pengajuan Cuti Menunggu Persetujuan Final ({count})</h3>
  <button onClick={() => setExportModal(true)}>
    <Download className="w-4 h-4" />
    <span>Download Rekap</span>
  </button>
</div>
```

#### **EnhancedAdminPanel (Disdik):**
```typescript
{/* Quick Actions */}
<div className="flex flex-wrap gap-3">
  <button onClick={() => setExportModal(true)}>
    <Download className="w-4 h-4" />
    <span>Download Rekap Excel</span>
  </button>
  // ... other actions
</div>
```

## 📊 **Data Flow & Processing**

### **🔗 Drive Link Processing:**
```
1. User input link Google Drive
2. Validation: URL format check
3. Store in database: driveLink field
4. Admin access: Click link → opens in new tab
5. Download: Direct link to Google Drive
```

### **📈 Excel Export Processing:**
```
1. User click "Download Rekap"
2. Modal opens with filters
3. User select month/year/unit
4. Preview shows record count
5. Click "Download Excel"
6. Filter data by criteria
7. Generate Excel with XLSX library
8. Format Indonesian dates/text
9. Add summary statistics
10. Download file to user's device
```

## 🎯 **Business Benefits**

### **📁 Drive Link Benefits:**
- ✅ **Flexibility**: User dapat upload dokumen besar atau multiple files
- ✅ **Convenience**: Tidak perlu download-upload jika sudah di Drive
- ✅ **Storage Efficiency**: Tidak menggunakan storage sistem internal
- ✅ **Collaboration**: Admin dapat akses folder dengan multiple dokumen

### **📊 Excel Export Benefits:**
- ✅ **Reporting**: Format Excel siap untuk laporan resmi
- ✅ **Analysis**: Data dapat diolah lebih lanjut di Excel
- ✅ **Archive**: File dapat disimpan sebagai arsip bulanan
- ✅ **Sharing**: Mudah dibagikan ke stakeholder lain

## 🔒 **Security & Validation**

### **🔗 Drive Link Security:**
- ✅ **URL Validation**: Check format Google Drive URL
- ✅ **Access Control**: User bertanggung jawab set permission
- ✅ **Fallback**: Tetap ada opsi upload file langsung
- ✅ **Admin Verification**: Admin dapat verifikasi akses link

### **📊 Excel Export Security:**
- ✅ **Role-based Access**: Export sesuai kewenangan user
- ✅ **Data Filtering**: Hanya data yang berhak diakses
- ✅ **No Sensitive Data**: Tidak ada password/credential di export
- ✅ **Audit Trail**: Log export activity

## 🧪 **Testing Scenarios**

### **✅ Drive Link Testing:**
1. **Valid Google Drive File Link** ✅
2. **Valid Google Drive Folder Link** ✅
3. **Invalid URL Format** ❌ (validation error)
4. **Empty Link** ✅ (optional field)
5. **Link + File Upload** ✅ (both allowed)

### **✅ Excel Export Testing:**
1. **Export Current Month** ✅
2. **Export Previous Month** ✅
3. **Export Specific Unit** ✅ (Disdik)
4. **Export All Units** ✅ (Disdik)
5. **Export Empty Period** ✅ (0 records)
6. **Large Dataset Export** ✅ (performance test)

## 📋 **File Naming Convention**

### **📊 Excel Export Files:**
```
Format: Rekap_Cuti_{Bulan}_{Tahun}_{Unit}.xlsx

Examples:
- Rekap_Cuti_Januari_2024.xlsx (all units)
- Rekap_Cuti_Februari_2024_SMP_Negeri_1_Grobogan.xlsx (specific unit)
- Rekap_Cuti_Maret_2024_Koordinator_Wilayah_Purwodadi.xlsx (coordinator)
```

---

## ✅ **STATUS: DRIVE LINK & EXCEL EXPORT IMPLEMENTED**

**Drive Link**: ✅ Field tambahan untuk link Google Drive (file/folder)
**Excel Export**: ✅ Download rekap cuti dalam format Excel dengan template lengkap
**Role-based Access**: ✅ Export sesuai kewenangan masing-masing admin
**User Experience**: ✅ Interface yang intuitif dan informatif

**Key Features:**
- 🔗 **Flexible Upload**: File langsung atau link Google Drive
- 📊 **Professional Reports**: Format Excel siap untuk laporan resmi
- 🎯 **Role-based Export**: Data sesuai kewenangan user
- 📱 **User-friendly UI**: Modal yang informatif dengan preview

Sistem sekarang mendukung upload dokumen yang lebih fleksibel dan export laporan yang profesional!
