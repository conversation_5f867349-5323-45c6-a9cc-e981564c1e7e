<!DOCTYPE html>
<html lang="id">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Sistem Pengajuan Cuti ASN Guru</title>
   <style>
       :root {
           --primary-color: #3498db;
           --secondary-color: #2ecc71;
           --danger-color: #e74c3c;
           --warning-color: #f39c12;
           --dark-color: #2c3e50;
           --light-color: #ecf0f1;
           --gray-color: #95a5a6;
           --admin-color: #9b59b6;
           --coordinator-color: #e67e22;
       }

       * {
           margin: 0;
           padding: 0;
           box-sizing: border-box;
           font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
       }

       body {
           background-color: #f8f9fa;
           color: #333;
           line-height: 1.6;
       }

       .container {
           max-width: 1200px;
           margin: 0 auto;
           padding: 0 20px;
       }

       header {
           background-color: var(--dark-color);
           color: white;
           padding: 1rem 0;
           box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
       }

       .header-content {
           display: flex;
           justify-content: space-between;
           align-items: center;
       }

       .logo {
           font-size: 1.5rem;
           font-weight: bold;
       }

       nav ul {
           display: flex;
           list-style: none;
       }

       nav ul li {
           margin-left: 1.5rem;
       }

       nav ul li a {
           color: white;
           text-decoration: none;
           transition: color 0.3s;
       }

       nav ul li a:hover {
           color: var(--light-color);
       }

       .main-content {
           padding: 2rem 0;
       }

       .card {
           background-color: white;
           border-radius: 8px;
           box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
           padding: 2rem;
           margin-bottom: 2rem;
           display: none; /* Hidden by default, controlled by JS */
       }

       .card.active {
           display: block; /* Show active card */
       }

       .card-title {
           font-size: 1.5rem;
           margin-bottom: 1rem;
           color: var(--dark-color);
           border-bottom: 2px solid var(--light-color);
           padding-bottom: 0.5rem;
       }

       .form-group {
           margin-bottom: 1.5rem;
       }

       label {
           display: block;
           margin-bottom: 0.5rem;
           font-weight: 600;
       }

       input, select, textarea {
           width: 100%;
           padding: 0.75rem;
           border: 1px solid #ddd;
           border-radius: 4px;
           font-size: 1rem;
       }

       textarea {
           min-height: 150px;
           resize: vertical;
       }

       button {
           background-color: var(--primary-color);
           color: white;
           border: none;
           padding: 0.75rem 1.5rem;
           border-radius: 4px;
           cursor: pointer;
           font-size: 1rem;
           transition: background-color 0.3s;
       }

       button:hover {
           background-color: #2980b9;
       }

       .btn-secondary {
           background-color: var(--secondary-color);
       }

       .btn-secondary:hover {
           background-color: #27ae60;
       }

       .btn-danger {
           background-color: var(--danger-color);
       }

       .btn-danger:hover {
           background-color: #c0392b;
       }

       .btn-warning {
           background-color: var(--warning-color);
       }

       .btn-warning:hover {
           background-color: #d35400;
       }

       .btn-admin {
           background-color: var(--admin-color);
       }

       .btn-admin:hover {
           background-color: #8e44ad;
       }

       .btn-coordinator {
           background-color: var(--coordinator-color);
       }

       .btn-coordinator:hover {
           background-color: #d35400;
       }

       .status-badge {
           display: inline-block;
           padding: 0.25rem 0.5rem;
           border-radius: 4px;
           font-size: 0.875rem;
           font-weight: 600;
       }

       .status-pending {
           background-color: #f39c12;
           color: white;
       }

       .status-approved-coordinator {
           background-color: var(--coordinator-color);
           color: white;
       }

       .status-approved-admin {
           background-color: var(--admin-color);
           color: white;
       }

       .status-rejected {
           background-color: #e74c3c;
           color: white;
       }

       .file-upload {
           border: 2px dashed #ddd;
           border-radius: 4px;
           padding: 1.5rem;
           text-align: center;
           cursor: pointer;
           transition: border-color 0.3s;
       }

       .file-upload:hover {
           border-color: var(--primary-color);
       }

       .file-upload i {
           font-size: 3rem;
           color: var(--primary-color);
           margin-bottom: 1rem;
       }

       .file-list {
           margin-top: 1rem;
       }

       .file-item {
           display: flex;
           justify-content: space-between;
           align-items: center;
           padding: 0.5rem 0;
           border-bottom: 1px solid #eee;
       }

       .file-name {
           flex-grow: 1;
           margin-right: 1rem;
       }

       .file-actions button {
           padding: 0.25rem 0.5rem;
           font-size: 0.875rem;
       }

       .dashboard {
           display: grid;
           grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
           gap: 1.5rem;
       }

       .dashboard-card {
           background-color: white;
           border-radius: 8px;
           box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
           padding: 1.5rem;
       }

       .dashboard-card-title {
           font-size: 1.25rem;
           margin-bottom: 1rem;
           color: var(--dark-color);
       }

       .dashboard-card-content {
           font-size: 1.5rem;
           font-weight: bold;
           color: var(--primary-color);
       }

       footer {
           background-color: var(--dark-color);
           color: white;
           text-align: center;
           padding: 1rem 0;
           margin-top: 2rem;
       }

       @media (max-width: 768px) {
           .header-content {
               flex-direction: column;
           }

           nav ul {
               margin-top: 1rem;
           }

           nav ul li {
               margin-left: 1rem;
               margin-right: 1rem;
           }

           .dashboard {
               grid-template-columns: 1fr;
           }
       }

       /* Admin and Coordinator panels */
       .panel {
           display: none;
       }

       .panel.active {
           display: block;
       }

       .role-switcher {
           margin-bottom: 1rem;
       }

       .role-switcher button {
           margin-right: 0.5rem;
       }

       /* Table styling */
       table {
           width: 100%;
           border-collapse: collapse;
           margin-top: 1rem;
       }

       th, td {
           padding: 0.75rem;
           text-align: left;
           border-bottom: 1px solid #ddd;
       }

       th {
           background-color: var(--light-color);
       }

       tr:hover {
           background-color: #f5f5f5;
       }

       /* Modal styles */
       .modal {
           display: none; /* Hidden by default */
           position: fixed; /* Stay in place */
           z-index: 1000; /* Sit on top */
           left: 0;
           top: 0;
           width: 100%; /* Full width */
           height: 100%; /* Full height */
           overflow: auto; /* Enable scroll if needed */
           background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
           justify-content: center;
           align-items: center;
       }

       .modal-content {
           background-color: #fefefe;
           margin: auto;
           padding: 20px;
           border: 1px solid #888;
           width: 80%;
           max-width: 500px;
           border-radius: 8px;
           box-shadow: 0 4px 8px rgba(0,0,0,0.2);
           text-align: center;
           position: relative;
       }

       .close-button {
           color: #aaa;
           float: right;
           font-size: 28px;
           font-weight: bold;
           position: absolute;
           top: 10px;
           right: 20px;
           cursor: pointer;
       }

       .close-button:hover,
       .close-button:focus {
           color: black;
           text-decoration: none;
           cursor: pointer;
       }

       /* Loading indicator */
       .loading-indicator {
           display: none;
           border: 4px solid #f3f3f3;
           border-top: 4px solid var(--primary-color);
           border-radius: 50%;
           width: 30px;
           height: 30px;
           animation: spin 1s linear infinite;
           margin: 10px auto;
       }

       @keyframes spin {
           0% { transform: rotate(0deg); }
           100% { transform: rotate(360deg); }
       }

       .modal-content.text-area {
           text-align: left;
       }
       .modal-content.text-area textarea {
           width: 100%;
           min-height: 150px;
           border: 1px solid #ddd;
           padding: 10px;
           border-radius: 4px;
           margin-bottom: 10px;
       }
       .modal-content.text-area .button-group {
           display: flex;
           justify-content: flex-end;
           gap: 10px;
       }
       .rejection-reason {
           color: var(--danger-color);
           font-size: 0.9em;
           margin-top: 5px;
       }
       .filter-group {
           display: flex;
           gap: 10px;
           margin-bottom: 1.5rem;
           align-items: flex-end;
       }
       .filter-group input {
           flex-grow: 1;
       }
       .filter-group button {
           white-space: nowrap;
       }
       #userStatusTableContainer { /* New container for the table */
           display: none; /* Hidden by default */
           margin-top: 1rem;
       }
       #userStatusTableContainer.active {
           display: block;
       }
   </style>
</head>
<body>
   <header>
       <div class="container header-content">
           <div class="logo">Sistem Pengajuan Cuti ASN Guru</div>
           <nav>
               <ul>
                   <li><a href="#beranda" data-section="beranda">🏠 Beranda</a></li>
                   <li><a href="#pengajuan" data-section="pengajuan">📝 Pengajuan Cuti</a></li>
                   <li><a href="#status" data-section="status">📊 Status Pengajuan</a></li>
                   <li><a href="#role" data-section="role">👤 Peran</a></li>
                   <li><a href="#tentang" data-section="tentang">ℹ️ Tentang</a></li>
               </ul>
           </nav>
       </div>
   </header>

   <main class="main-content">
       <div class="container">
           <section id="beranda" class="card active">
               <h2 class="card-title">Selamat Datang di Sistem Pengajuan Cuti ASN Guru</h2>
               <p>Sistem ini memudahkan ASN Guru untuk mengajukan cuti secara online yang akan diproses secara berjenjang mulai dari Koordinator Wilayah hingga Dinas Pendidikan.</p>
               <div class="dashboard">
                   <div class="dashboard-card">
                       <h3 class="dashboard-card-title">Pengajuan Baru</h3>
                       <div class="dashboard-card-content" id="dashboardNew">0</div>
                   </div>
                   <div class="dashboard-card">
                       <h3 class="dashboard-card-title">Pending</h3>
                       <div class="dashboard-card-content" id="dashboardPending">0</div>
                   </div>
                   <div class="dashboard-card">
                       <h3 class="dashboard-card-title">Disetujui Koordinator</h3>
                       <div class="dashboard-card-content" id="dashboardApprovedCoordinator">0</div>
                   </div>
                   <div class="dashboard-card">
                       <h3 class="dashboard-card-title">Disetujui Dinas</h3>
                       <div class="dashboard-card-content" id="dashboardApprovedAdmin">0</div>
                   </div>
               </div>
           </section>

           <section id="pengajuan" class="card">
               <h2 class="card-title">Form Pengajuan Cuti</h2>
               <form id="cutiForm">
                   <input type="hidden" id="requestId" name="requestId">
                   <div class="form-group">
                       <label for="nama">Nama Lengkap</label>
                       <input type="text" id="nama" name="nama" required>
                   </div>
                   <div class="form-group">
                       <label for="nip">NIP</label>
                       <input type="text" id="nip" name="nip" required>
                   </div>
                   <div class="form-group">
                       <label for="jabatan">Jabatan</label>
                       <input type="text" id="jabatan" name="jabatan" required>
                   </div>
                   <div class="form-group">
                       <label for="sekolah">Sekolah</label>
                       <input type="text" id="sekolah" name="sekolah" required>
                   </div>
                   <div class="form-group">
                       <label for="koordinatorWilayah">Koordinator Wilayah</label>
                       <select id="koordinatorWilayah" name="koordinatorWilayah" required>
                           <option value="">Pilih Koordinator Wilayah</option>
                           <option value="Koordinator Wilayah 1">Koordinator Wilayah 1</option>
                           <option value="Koordinator Wilayah 2">Koordinator Wilayah 2</option>
                           <option value="Koordinator Wilayah 3">Koordinator Wilayah 3</option>
                       </select>
                   </div>
                   <div class="form-group">
                       <label for="jenisCuti">Jenis Cuti</label>
                       <select id="jenisCuti" name="jenisCuti" required>
                           <option value="">Pilih Jenis Cuti</option>
                           <option value="Cuti Tahunan">Cuti Tahunan</option>
                           <option value="Cuti Sakit">Cuti Sakit</option>
                           <option value="Cuti Besar">Cuti Besar</option>
                           <option value="Cuti Alasan Penting">Cuti Alasan Penting</option>
                           <option value="Cuti Diluar Tanggungan Negara">Cuti Diluar Tanggungan Negara</option>
                       </select>
                   </div>
                   <div class="form-group">
                       <label for="tanggalMulai">Tanggal Mulai Cuti</label>
                       <input type="date" id="tanggalMulai" name="tanggalMulai" required>
                   </div>
                   <div class="form-group">
                       <label for="tanggalSelesai">Tanggal Selesai Cuti</label>
                       <input type="date" id="tanggalSelesai" name="tanggalSelesai" required>
                   </div>
                   <div class="form-group">
                       <label for="alasanCuti">Alasan Cuti</label>
                       <textarea id="alasanCuti" name="alasanCuti" required></textarea>
                   </div>
                   <div class="form-group">
                       <label>Upload Berkas Pendukung</label>
                       <div class="file-upload" id="fileUpload">
                           <i>📁</i>
                           <p>Klik untuk memilih file atau seret file ke sini</p>
                           <input type="file" id="fileInput" style="display: none;" multiple>
                       </div>
                       <div class="file-list" id="fileList">
                           <!-- File list will be populated here -->
                       </div>
                   </div>
                   <button type="submit" class="btn-secondary" id="submitCutiBtn">Ajukan Pengajuan</button>
               </form>
           </section>

           <section id="status" class="card">
               <h2 class="card-title">Status Pengajuan Cuti Anda</h2>
               <div class="filter-group">
                   <label for="filterNip" style="margin-bottom: 0;">Cari berdasarkan NIP:</label>
                   <input type="text" id="filterNip" placeholder="Masukkan NIP Anda">
                   <button class="btn-primary" id="applyNipFilterBtn">Cari</button>
                   <button class="btn-secondary" id="clearNipFilterBtn">Reset</button>
               </div>
               <div id="userStatusTableContainer">
                   <table id="userStatusTable">
                       <thead>
                           <tr>
                               <th>No</th>
                               <th>Tanggal Pengajuan</th>
                               <th>Jenis Cuti</th>
                               <th>Tanggal Cuti</th>
                               <th>Sekolah</th>
                               <th>Koordinator Wilayah</th>
                               <th>Status</th>
                               <th>Aksi</th>
                           </tr>
                       </thead>
                       <tbody>
                           <!-- User's leave requests will be populated here -->
                       </tbody>
                   </table>
               </div>
               <p id="nipFilterMessage" style="text-align: center; margin-top: 20px; color: var(--gray-color);">Silakan masukkan NIP untuk melihat status pengajuan Anda.</p>
           </section>

           <section id="role" class="card">
               <h2 class="card-title">Panel Peran</h2>
               <div class="role-switcher">
                   <button class="btn-admin" onclick="switchRole('admin')">Panel Admin Dinas</button>
                   <button class="btn-coordinator" onclick="switchRole('coordinator')">Panel Koordinator Wilayah</button>
                   <button class="btn-secondary" onclick="switchRole('user')">Mode Pengguna</button>
               </div>

               <div id="userPanel" class="panel active">
                   <p>Anda sedang menggunakan mode pengguna standar. Untuk mengakses fungsi administratif, pilih peran di atas.</p>
               </div>

               <div id="adminPanel" class="panel admin-panel">
                   <h3 class="dashboard-card-title">Panel Admin Dinas Pendidikan</h3>
                   <p>Sebagai admin dinas pendidikan, Anda dapat:</p>
                   <ul style="margin-left: 1.5rem; margin-top: 1rem;">
                       <li style="margin-bottom: 0.5rem;">Melihat semua pengajuan cuti dari seluruh wilayah</li>
                       <li style="margin-bottom: 0.5rem;">Menyetujui atau menolak pengajuan cuti yang sudah disetujui koordinator wilayah</li>
                       <li style="margin-bottom: 0.5rem;">Melihat statistik pengajuan cuti di seluruh wilayah</li>
                   </ul>
                   <table id="adminPanelTable">
                       <thead>
                           <tr>
                               <th>No</th>
                               <th>Nama Guru</th>
                               <th>NIP</th> <!-- Added NIP column -->
                               <th>Sekolah</th>
                               <th>Koordinator Wilayah</th>
                               <th>Jenis Cuti</th>
                               <th>Tanggal Cuti</th>
                               <th>Status Koordinator</th>
                               <th>Berkas</th>
                               <th>Aksi</th>
                           </tr>
                       </thead>
                       <tbody>
                           <!-- Admin's view of leave requests will be populated here -->
                       </tbody>
                   </table>
               </div>

               <div id="coordinatorPanel" class="panel coordinator-panel">
                   <h3 class="dashboard-card-title">Panel Koordinator Wilayah</h3>
                   <p>Sebagai koordinator wilayah, Anda dapat:</p>
                   <ul style="margin-left: 1.5rem; margin-top: 1rem;">
                       <li style="margin-bottom: 0.5rem;">Melihat pengajuan cuti dari sekolah-sekolah di wilayah Anda</li>
                       <li style="margin-bottom: 0.5rem;">Menyetujui atau menolak pengajuan cuti sesuai dengan kriteria yang berlaku</li>
                       <li style="margin-bottom: 0.5rem;">Melihat statistik pengajuan cuti di wilayah Anda</li>
                   </ul>
                   <table id="coordinatorPanelTable">
                       <thead>
                           <tr>
                               <th>No</th>
                               <th>Nama Guru</th>
                               <th>NIP</th> <!-- Added NIP column for Coordinator Panel -->
                               <th>Sekolah</th>
                               <th>Jenis Cuti</th>
                               <th>Tanggal Cuti</th>
                               <th>Status</th>
                               <th>Berkas</th>
                               <th>Aksi</th>
                           </tr>
                       </thead>
                       <tbody>
                           <!-- Coordinator's view of leave requests will be populated here -->
                       </tbody>
                   </table>
               </div>
           </section>

           <section id="tentang" class="card">
               <h2 class="card-title">Tentang Sistem</h2>
               <p>Sistem Pengusulan Cuti ASN Guru ini dikembangkan oleh Dinas Pendidikan untuk memudahkan proses pengajuan cuti bagi ASN Guru. Sistem ini memungkinkan:</p>
               <ul style="margin-left: 1.5rem; margin-top: 1rem;">
                   <li style="margin-bottom: 0.5rem;">Pengajuan cuti secara online</li>
                   <li style="margin-bottom: 0.5rem;">Pengunggahan berkas pendukung</li>
                   <li style="margin-bottom: 0.5rem;">Proses persetujuan berjenjang (Koordinator Wilayah hingga Dinas Pendidikan)</li>
                   <li style="margin-bottom: 0.5rem;">Pemantauan status pengajuan cuti</li>
               </ul>
               <p style="margin-top: 1rem;">Sistem ini bertujuan untuk meningkatkan efisiensi dan transparansi dalam proses pengajuan cuti ASN Guru.</p>
           </section>
       </div>
   </main>

   <footer>
       <div class="container">
           <p>© 2023 Dinas Pendidikan. Semua hak dilindungi.</p>
       </div>
   </footer>

   <!-- Custom Modal for general messages -->
   <div id="customModal" class="modal">
       <div class="modal-content">
           <span class="close-button" onclick="closeModal()">×</span>
           <p id="modalMessage"></p>
           <button onclick="closeModal()">OK</button>
       </div>
   </div>

   <!-- Modal for Rejection Reason Input -->
   <div id="rejectionReasonModal" class="modal">
       <div class="modal-content text-area">
           <span class="close-button" onclick="closeRejectionReasonModal()">×</span>
           <h3>Alasan Penolakan</h3>
           <textarea id="rejectionReasonInput" placeholder="Masukkan alasan penolakan..."></textarea>
           <div class="button-group">
               <button class="btn-danger" id="submitRejectionReasonBtn">Kirim Penolakan</button>
               <button class="btn-secondary" onclick="closeRejectionReasonModal()">Batal</button>
           </div>
       </div>
   </div>

   <script>
       // Global variables for file handling
       let uploadedFiles = [];
       let fileInput; // Will be assigned in DOMContentLoaded
       let leaveRequests = []; // Array to store leave requests
       let currentEditingRequestId = null; // To track which request is being edited
       let currentRejectionRequestId = null; // To track which request is being rejected
       let currentRejectionRole = null; // To track which role is rejecting
       let currentNipFilter = ''; // Stores the NIP filter for user status table

       document.addEventListener('DOMContentLoaded', function() {
           fileInput = document.getElementById('fileInput');
           const fileUpload = document.getElementById('fileUpload');
           const fileList = document.getElementById('fileList');
           const cutiForm = document.getElementById('cutiForm');
           const navLinks = document.querySelectorAll('nav ul li a');
           const sections = document.querySelectorAll('.main-content .card');
           const submitCutiBtn = document.getElementById('submitCutiBtn');
           const requestIdInput = document.getElementById('requestId');

           // Modal elements for general messages
           const customModal = document.getElementById('customModal');
           const modalMessage = document.getElementById('modalMessage');

           // Modal elements for rejection reason
           const rejectionReasonModal = document.getElementById('rejectionReasonModal');
           const rejectionReasonInput = document.getElementById('rejectionReasonInput');
           const submitRejectionReasonBtn = document.getElementById('submitRejectionReasonBtn');

           // NIP filter elements
           const filterNipInput = document.getElementById('filterNip');
           const applyNipFilterBtn = document.getElementById('applyNipFilterBtn');
           const clearNipFilterBtn = document.getElementById('clearNipFilterBtn');
           const userStatusTableContainer = document.getElementById('userStatusTableContainer');
           const nipFilterMessage = document.getElementById('nipFilterMessage');

           // --- Initial Dummy Data (for demonstration) ---
           leaveRequests = [
               {
                   id: 'req1',
                   nama: 'Dra. Sri Wahyuni',
                   nip: '197001011995012001',
                   jabatan: 'Guru Kelas',
                   sekolah: 'SDN 1 Jakarta',
                   koordinatorWilayah: 'Koordinator Wilayah 1',
                   jenisCuti: 'Cuti Tahunan',
                   tanggalMulai: '2023-05-15',
                   tanggalSelesai: '2023-05-20',
                   alasanCuti: 'Mengunjungi keluarga di luar kota.',
                   files: [{ name: 'Surat Permohonan.pdf', url: '#' }],
                   status: 'approved_coordinator',
                   rejectionReason: ''
               },
               {
                   id: 'req2',
                   nama: 'Drs. Budi Santoso',
                   nip: '197503102000051002',
                   jabatan: 'Guru Matematika',
                   sekolah: 'SMPN 2 Bandung',
                   koordinatorWilayah: 'Koordinator Wilayah 2',
                   jenisCuti: 'Cuti Sakit',
                   tanggalMulai: '2023-06-10',
                   tanggalSelesai: '2023-06-12',
                   alasanCuti: 'Sakit demam tinggi, memerlukan istirahat total.',
                   files: [{ name: 'Surat Dokter.jpg', url: '#' }],
                   status: 'approved_admin',
                   rejectionReason: ''
               },
               {
                   id: 'req3',
                   nama: 'Siti Aminah, S.Pd.',
                   nip: '198007202005012003',
                   jabatan: 'Guru Bahasa Indonesia',
                   sekolah: 'SMA Negeri 3 Surabaya',
                   koordinatorWilayah: 'Koordinator Wilayah 3',
                   jenisCuti: 'Cuti Alasan Penting',
                   tanggalMulai: '2023-07-20',
                   tanggalSelesai: '2023-07-25',
                   alasanCuti: 'Menghadiri pernikahan adik kandung di luar kota.',
                   files: [],
                   status: 'pending',
                   rejectionReason: ''
               },
               {
                   id: 'req4',
                   nama: 'Agus Setiawan, M.Pd.',
                   nip: '198509012010011004',
                   jabatan: 'Guru Fisika',
                   sekolah: 'SMK Negeri 1 Semarang',
                   koordinatorWilayah: 'Koordinator Wilayah 1',
                   jenisCuti: 'Cuti Tahunan',
                   tanggalMulai: '2023-08-01',
                   tanggalSelesai: '2023-08-07',
                   alasanCuti: 'Liburan keluarga ke Bali.',
                   files: [],
                   status: 'rejected',
                   rejectionReason: 'Jadwal cuti bentrok dengan kegiatan sekolah penting.'
               }
           ];

           // --- Function to show a specific section and hide others ---
           window.showSection = function(sectionId) {
               sections.forEach(section => {
                   section.classList.remove('active');
               });
               document.getElementById(sectionId).classList.add('active');

               // If switching away from the role section, reset role panels to user
               if (sectionId !== 'role') {
                   switchRole('user');
               }

               // Re-render tables when switching sections to ensure data is fresh
               renderDashboard();
               renderUserStatusTable(); // This will now use the currentNipFilter
               renderCoordinatorPanelTable();
               renderAdminPanelTable();
           };

           // --- Event listeners for navigation links ---
           navLinks.forEach(link => {
               link.addEventListener('click', function(e) {
                   e.preventDefault(); // Prevent default anchor link behavior (scrolling)
                   const sectionId = this.getAttribute('data-section');
                   showSection(sectionId);
               });
           });

           // --- File upload functionality ---
           fileUpload.addEventListener('click', function() {
               fileInput.click();
           });

           fileInput.addEventListener('change', function() {
               const maxFileSize = 1 * 1024 * 1024; // 1MB in bytes
               let filesAdded = false;

               for (let i = 0; i < this.files.length; i++) {
                   const file = this.files[i];
                   if (file.size > maxFileSize) {
                       showModal(`Ukuran file "${file.name}" (${(file.size / (1024 * 1024)).toFixed(2)} MB) melebihi batas maksimal 1MB.`);
                   } else {
                       uploadedFiles.push(file);
                       filesAdded = true;
                   }
               }
               if (filesAdded) {
                   renderFileList();
               }
               // Clear the file input value to allow re-uploading the same file after rejection
               this.value = '';
           });

           // Function to render the list of uploaded files
           function renderFileList() {
               fileList.innerHTML = ''; // Clear current list
               uploadedFiles.forEach((file, index) => {
                   const fileItem = document.createElement('div');
                   fileItem.className = 'file-item';
                   fileItem.innerHTML = `
                       <span class="file-name">${file.name}</span>
                       <div class="file-actions">
                           <button type="button" class="btn-danger" onclick="removeFile(${index})">Hapus</button>
                       </div>
                   `;
                   fileList.appendChild(fileItem);
               });
           }

           // Function to remove file (needs to be in global scope)
           window.removeFile = function(indexToRemove) {
               uploadedFiles = uploadedFiles.filter((_, index) => index !== indexToRemove);
               renderFileList(); // Re-render the list after removal
           };

           // --- Form submission / Update logic ---
           cutiForm.addEventListener('submit', function(e) {
               e.preventDefault();

               // Form validation: Check if all required fields are filled
               const requiredInputs = cutiForm.querySelectorAll('[required]');
               let allFieldsFilled = true;
               for (let i = 0; i < requiredInputs.length; i++) {
                   if (!requiredInputs[i].value.trim()) {
                       allFieldsFilled = false;
                       break;
                   }
               }

               if (!allFieldsFilled) {
                   showModal('Mohon lengkapi semua kolom pengajuan cuti yang wajib diisi.');
                   return; // Stop form submission
               }

               const formData = {
                   nama: document.getElementById('nama').value,
                   nip: document.getElementById('nip').value,
                   jabatan: document.getElementById('jabatan').value,
                   sekolah: document.getElementById('sekolah').value,
                   koordinatorWilayah: document.getElementById('koordinatorWilayah').value,
                   jenisCuti: document.getElementById('jenisCuti').value,
                   tanggalMulai: document.getElementById('tanggalMulai').value,
                   tanggalSelesai: document.getElementById('tanggalSelesai').value,
                   alasanCuti: document.getElementById('alasanCuti').value,
                   files: uploadedFiles.map(f => ({ name: f.name, url: URL.createObjectURL(f) })), // Store file info
               };

               if (currentEditingRequestId) {
                   // Update existing request
                   const index = leaveRequests.findIndex(req => req.id === currentEditingRequestId);
                   if (index !== -1) {
                       leaveRequests[index] = {
                           ...leaveRequests[index],
                           ...formData,
                           status: 'pending', // Reset status to pending after edit
                           rejectionReason: '' // Clear rejection reason
                       };
                       showModal('Pengajuan cuti berhasil diperbarui dan dikirim ulang untuk persetujuan.');
                   }
                   currentEditingRequestId = null;
                   submitCutiBtn.textContent = 'Ajukan Pengajuan';
               } else {
                   // New submission
                   formData.id = 'req' + Date.now(); // Simple unique ID
                   formData.status = 'pending';
                   formData.rejectionReason = '';
                   leaveRequests.push(formData);
                   showModal('Pengajuan cuti telah berhasil disimpan. Pengajuan Anda akan diproses secara berjenjang.');
               }

               this.reset();
               uploadedFiles = []; // Clear uploaded files
               renderFileList(); // Clear file list display
               renderAllTables(); // Re-render all tables to reflect changes
               showSection('status'); // Go to status page after submission/update
           });

           // --- Role switching functionality ---
           window.switchRole = function(role) {
               // Hide all panels within the role section
               document.getElementById('userPanel').classList.remove('active');
               document.getElementById('adminPanel').classList.remove('active');
               document.getElementById('coordinatorPanel').classList.remove('active');

               // Show selected panel
               if (role === 'user') {
                   document.getElementById('userPanel').classList.add('active');
               } else if (role === 'admin') {
                   document.getElementById('adminPanel').classList.add('active');
               } else if (role === 'coordinator') {
                   document.getElementById('coordinatorPanel').classList.add('active');
               }
               renderAllTables(); // Re-render tables when role changes
           };

           // --- Modal functions for general messages ---
           window.showModal = function(message) {
               modalMessage.textContent = message;
               customModal.style.display = 'flex'; // Use flex to center the modal
           };

           window.closeModal = function() {
               customModal.style.display = 'none';
           };

           // Close modal if user clicks outside of it
           customModal.addEventListener('click', function(event) {
               if (event.target == customModal) {
                   closeModal();
               }
           });

           // --- Rejection Reason Modal functions ---
           window.showRejectionReasonModal = function(requestId, role) {
               currentRejectionRequestId = requestId;
               currentRejectionRole = role;
               rejectionReasonInput.value = ''; // Clear previous reason
               rejectionReasonModal.style.display = 'flex';
           };

           window.closeRejectionReasonModal = function() {
               rejectionReasonModal.style.display = 'none';
               currentRejectionRequestId = null;
               currentRejectionRole = null;
           };

           submitRejectionReasonBtn.addEventListener('click', function() {
               const reason = rejectionReasonInput.value.trim();
               if (reason) {
                   handleReject(currentRejectionRequestId, currentRejectionRole, reason);
                   closeRejectionReasonModal();
               } else {
                   showModal('Alasan penolakan tidak boleh kosong.');
               }
           });

           // --- Core Logic for Approval/Rejection/Download ---
           window.handleApprove = function(id, role) {
               const request = leaveRequests.find(req => req.id === id);
               if (!request) return;

               if (role === 'coordinator') {
                   request.status = 'approved_coordinator';
                   showModal(`Pengajuan cuti dari ${request.nama} telah disetujui oleh Koordinator Wilayah.`);
               } else if (role === 'admin') {
                   request.status = 'approved_admin';
                   showModal(`Pengajuan cuti dari ${request.nama} telah disetujui oleh Dinas Pendidikan.`);
               }
               renderAllTables();
           };

           window.handleReject = function(id, role, reason) {
               const request = leaveRequests.find(req => req.id === id);
               if (!request) return;

               request.status = 'rejected';
               request.rejectionReason = reason;
               showModal(`Pengajuan cuti dari ${request.nama} telah ditolak oleh ${role === 'coordinator' ? 'Koordinator Wilayah' : 'Admin Dinas'} dengan alasan: "${reason}"`);
               renderAllTables();
           };

           window.handleDownload = function(fileUrl, fileName) {
               // In a real app, you would initiate a file download here.
               // For this simulation, we'll just show a message.
               showModal(`Simulasi unduhan berkas: "${fileName}".`);
               // You could also open the URL in a new tab if it's a valid public URL:
               // window.open(fileUrl, '_blank');
           };

           // --- Edit Rejected Request ---
           window.editRejectedRequest = function(id) {
               const request = leaveRequests.find(req => req.id === id);
               if (!request) return;

               // Populate the form fields
               document.getElementById('nama').value = request.nama;
               document.getElementById('nip').value = request.nip;
               document.getElementById('jabatan').value = request.jabatan;
               document.getElementById('sekolah').value = request.sekolah;
               document.getElementById('koordinatorWilayah').value = request.koordinatorWilayah;
               document.getElementById('jenisCuti').value = request.jenisCuti;
               document.getElementById('tanggalMulai').value = request.tanggalMulai;
               document.getElementById('tanggalSelesai').value = request.tanggalSelesai;
               document.getElementById('alasanCuti').value = request.alasanCuti;

               // Set files for editing (re-upload might be needed in real app)
               uploadedFiles = [...request.files]; // Copy existing file info
               renderFileList();

               // Set the request ID for updating
               requestIdInput.value = request.id;
               currentEditingRequestId = request.id;
               submitCutiBtn.textContent = 'Perbarui Pengajuan';

               showSection('pengajuan'); // Navigate to the form
               showModal('Anda sedang memperbaiki pengajuan yang ditolak. Setelah diperbaiki, pengajuan akan dikirim ulang.');
           };

           // --- NIP Filter for User Status Table ---
           applyNipFilterBtn.addEventListener('click', function() {
               currentNipFilter = filterNipInput.value.trim();
               renderUserStatusTable();
           });

           clearNipFilterBtn.addEventListener('click', function() {
               filterNipInput.value = '';
               currentNipFilter = '';
               renderUserStatusTable();
           });

           // --- Rendering Functions for Tables and Dashboard ---

           function renderDashboard() {
               const newCount = leaveRequests.filter(req => req.status === 'pending').length;
               const pendingCount = leaveRequests.filter(req => req.status === 'pending').length; // Same as new for simplicity
               const approvedCoordinatorCount = leaveRequests.filter(req => req.status === 'approved_coordinator').length;
               const approvedAdminCount = leaveRequests.filter(req => req.status === 'approved_admin').length;

               document.getElementById('dashboardNew').textContent = newCount;
               document.getElementById('dashboardPending').textContent = pendingCount;
               document.getElementById('dashboardApprovedCoordinator').textContent = approvedCoordinatorCount;
               document.getElementById('dashboardApprovedAdmin').textContent = approvedAdminCount;
           }

           function renderUserStatusTable() {
               const tbody = document.querySelector('#userStatusTable tbody');
               tbody.innerHTML = ''; // Clear existing rows

               if (!currentNipFilter) {
                   userStatusTableContainer.classList.remove('active');
                   nipFilterMessage.style.display = 'block';
                   return; // Stop rendering if no NIP is entered
               }

               userStatusTableContainer.classList.add('active');
               nipFilterMessage.style.display = 'none';

               // Filter requests based on currentNipFilter
               const filteredRequests = leaveRequests.filter(req => req.nip === currentNipFilter);

               if (filteredRequests.length === 0) {
                   const row = tbody.insertRow();
                   const cell = row.insertCell();
                   cell.colSpan = 8;
                   cell.textContent = 'Tidak ada pengajuan cuti ditemukan untuk NIP ini.';
                   cell.style.textAlign = 'center';
               }

               filteredRequests.forEach((req, index) => {
                   const row = tbody.insertRow();
                   row.insertCell().textContent = index + 1;
                   row.insertCell().textContent = new Date().toLocaleDateString('id-ID'); // Assuming submission date is current date for dummy data
                   row.insertCell().textContent = req.jenisCuti;
                   row.insertCell().textContent = `${req.tanggalMulai} s/d ${req.tanggalSelesai}`;
                   row.insertCell().textContent = req.sekolah;
                   row.insertCell().textContent = req.koordinatorWilayah;

                   const statusCell = row.insertCell();
                   let statusClass = '';
                   let statusText = '';
                   if (req.status === 'pending') {
                       statusClass = 'status-pending';
                       statusText = 'Pending';
                   } else if (req.status === 'approved_coordinator') {
                       statusClass = 'status-approved-coordinator';
                       statusText = 'Disetujui Koordinator';
                   } else if (req.status === 'approved_admin') {
                       statusClass = 'status-approved-admin';
                       statusText = 'Disetujui Dinas';
                   } else if (req.status === 'rejected') {
                       statusClass = 'status-rejected';
                       statusText = 'Ditolak';
                   }
                   statusCell.innerHTML = `<span class="status-badge ${statusClass}">${statusText}</span>`;
                   if (req.status === 'rejected' && req.rejectionReason) {
                       statusCell.innerHTML += `<div class="rejection-reason">Alasan: ${req.rejectionReason}</div>`;
                   }

                   const actionCell = row.insertCell();
                   if (req.status === 'rejected') {
                       actionCell.innerHTML = `<button class="btn-warning" onclick="editRejectedRequest('${req.id}')">Perbaiki Pengajuan</button>`;
                   } else {
                       actionCell.innerHTML = `<button class="btn-warning" onclick="showModal('Detail pengajuan untuk ${req.nama}')">Detail</button>`;
                   }
               });
           }

           function renderCoordinatorPanelTable() {
               const tbody = document.querySelector('#coordinatorPanelTable tbody');
               tbody.innerHTML = ''; // Clear existing rows

               const coordinatorRequests = leaveRequests.filter(req =>
                   req.status === 'pending' // Coordinator only sees pending requests for their approval
               );

               if (coordinatorRequests.length === 0) {
                   const row = tbody.insertRow();
                   const cell = row.insertCell();
                   cell.colSpan = 9; /* Adjusted colspan for new NIP column */
                   cell.textContent = 'Tidak ada pengajuan cuti pending di wilayah Anda.';
                   cell.style.textAlign = 'center';
               }

               coordinatorRequests.forEach((req, index) => {
                   const row = tbody.insertRow();
                   row.insertCell().textContent = index + 1;
                   row.insertCell().textContent = req.nama;
                   row.insertCell().textContent = req.nip; // Display NIP for Coordinator Panel
                   row.insertCell().textContent = req.sekolah;
                   row.insertCell().textContent = req.jenisCuti;
                   row.insertCell().textContent = `${req.tanggalMulai} s/d ${req.tanggalSelesai}`;

                   const statusCell = row.insertCell();
                   statusCell.innerHTML = `<span class="status-badge status-pending">Pending</span>`;

                   const filesCell = row.insertCell();
                   if (req.files && req.files.length > 0) {
                       filesCell.innerHTML = req.files.map(file =>
                           `<button class="btn-secondary" style="margin-right:5px;" onclick="handleDownload('${file.url}', '${file.name}')">Unduh ${file.name.substring(0, 10)}...</button>`
                       ).join('');
                   } else {
                       filesCell.textContent = 'Tidak ada';
                   }

                   const actionCell = row.insertCell();
                   actionCell.innerHTML = `
                       <button class="btn-coordinator" onclick="handleApprove('${req.id}', 'coordinator')">Setujui</button>
                       <button class="btn-danger" onclick="showRejectionReasonModal('${req.id}', 'coordinator')">Tolak</button>
                   `;
               });
           }

           function renderAdminPanelTable() {
               const tbody = document.querySelector('#adminPanelTable tbody');
               tbody.innerHTML = ''; // Clear existing rows

               const adminRequests = leaveRequests.filter(req =>
                   req.status === 'approved_coordinator' // Admin only sees requests approved by coordinator
               );

               if (adminRequests.length === 0) {
                   const row = tbody.insertRow();
                   const cell = row.insertCell();
                   cell.colSpan = 10; /* Adjusted colspan for new NIP column */
                   cell.textContent = 'Tidak ada pengajuan cuti yang menunggu persetujuan dinas.';
                   cell.style.textAlign = 'center';
               }

               adminRequests.forEach((req, index) => {
                   const row = tbody.insertRow();
                   row.insertCell().textContent = index + 1;
                   row.insertCell().textContent = req.nama;
                   row.insertCell().textContent = req.nip; // Display NIP
                   row.insertCell().textContent = req.sekolah;
                   row.insertCell().textContent = req.koordinatorWilayah;
                   row.insertCell().textContent = req.jenisCuti;
                   row.insertCell().textContent = `${req.tanggalMulai} s/d ${req.tanggalSelesai}`;

                   const statusCell = row.insertCell();
                   statusCell.innerHTML = `<span class="status-badge status-approved-coordinator">Disetujui Koordinator</span>`;

                   const filesCell = row.insertCell();
                   if (req.files && req.files.length > 0) {
                       filesCell.innerHTML = req.files.map(file =>
                           `<button class="btn-secondary" style="margin-right:5px;" onclick="handleDownload('${file.url}', '${file.name}')">Unduh ${file.name.substring(0, 10)}...</button>`
                       ).join('');
                   } else {
                       filesCell.textContent = 'Tidak ada';
                   }

                   const actionCell = row.insertCell();
                   actionCell.innerHTML = `
                       <button class="btn-admin" onclick="handleApprove('${req.id}', 'admin')">Setujui Dinas</button>
                       <button class="btn-danger" onclick="showRejectionReasonModal('${req.id}', 'admin')">Tolak</button>
                   `;
               });
           }

           function renderAllTables() {
               renderDashboard();
               renderUserStatusTable();
               renderCoordinatorPanelTable();
               renderAdminPanelTable();
           }

           // Initial render when page loads
           renderAllTables();
           switchRole('user'); // Default to user view
       });
   </script>
</body>
</html>