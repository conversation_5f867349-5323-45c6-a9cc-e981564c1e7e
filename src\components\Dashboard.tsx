import React from 'react';
import { FileText, Clock, CheckCircle, Award } from 'lucide-react';
import { LeaveRequest } from '../types';

interface DashboardProps {
  leaveRequests: LeaveRequest[];
}

const Dashboard: React.FC<DashboardProps> = ({ leaveRequests }) => {
  const stats = {
    new: leaveRequests.filter(req => req.status === 'pending').length,
    pending: leaveRequests.filter(req => req.status === 'pending').length,
    approvedCoordinator: leaveRequests.filter(req => req.status === 'approved_coordinator').length,
    approvedAdmin: leaveRequests.filter(req => req.status === 'approved_admin').length,
  };

  const statCards = [
    {
      title: 'Pengajuan Baru',
      value: stats.new,
      icon: FileText,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700'
    },
    {
      title: 'Pending',
      value: stats.pending,
      icon: Clock,
      color: 'bg-yellow-500',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-700'
    },
    {
      title: 'Disetujui Koordinator',
      value: stats.approvedCoordinator,
      icon: CheckCircle,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-700'
    },
    {
      title: 'Disetujui Dinas',
      value: stats.approvedAdmin,
      icon: Award,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-700'
    },
  ];

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Selamat Datang di Si CERDAS
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Sistem Cuti Elektronik Dinas Pendidikan Kabupaten Grobogan
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div
              key={index}
              className={`${card.bgColor} rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm font-medium ${card.textColor} mb-1`}>
                    {card.title}
                  </p>
                  <p className={`text-3xl font-bold ${card.textColor}`}>
                    {card.value}
                  </p>
                </div>
                <div className={`${card.color} p-3 rounded-lg`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Dashboard;