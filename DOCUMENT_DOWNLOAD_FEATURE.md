# Document & Attachment Download Feature - Sistem Cuti ASN

## Overview
Fitur unduh dokumen dan lampiran lengkap untuk Admin Disdik yang memungkinkan:
1. **Unduh Surat Cuti** - Pembuatan dan pengunduhan surat izin cuti dalam format Word (.docx) menggunakan template
2. **Unduh Lampiran** - Pengunduhan file PDF yang diupload sebagai lampiran cuti

## 🚀 Fitur Utama

### 1. 📄 Template Word Integration
**Template Files Available:**
- `cuti_tahunan.docx` - Template untuk Cuti Tahunan
- `cuti_sakit14.docx` - Template untuk Cuti Sakit
- `cuti_alasanpenting.docx` - Template untuk Cuti Alasan Penting

**Template Variables (Akan Diganti):**
- `{nama}` - Nama lengkap pemohon
- `{nip}` - Nomor Induk P<PERSON>awai
- `{pangkat_golongan}` - Pangkat dan golongan
- `{jabatan}` - Jabatan/posisi
- `{sekolah}` - Unit kerja/sekolah
- `{lama_cuti}` - <PERSON><PERSON>i cuti (contoh: "5 Hari")
- `{tanggal_mulai}` - Tanggal mulai cuti (format Indonesia)
- `{tanggal_selesai}` - Tanggal selesai cuti (format Indonesia)
- `{alasan_cuti}` - Alasan cuti (khusus untuk cuti alasan penting)

**Template Variables (Akan Dipertahankan):**
- `{nomor_naskah}` - Nomor surat (tidak diubah)
- `{tanggal_naskah}` - Tanggal surat (tidak diubah)
- `{ttd_pengirim}` - Tanda tangan pengirim (tidak diubah)

### 2. 🔄 Automatic Document Generation
**Smart Template Selection:**
- **Cuti Tahunan** → `cuti_tahunan.docx`
- **Cuti Sakit** → `cuti_sakit14.docx`
- **Cuti Alasan Penting** → `cuti_alasanpenting.docx`
- **Cuti Melahirkan** → `cuti_tahunan.docx` (fallback)
- **Cuti Besar** → `cuti_tahunan.docx` (fallback)

**Data Processing:**
- **Automatic Duration Calculation** - Menghitung lama cuti dari tanggal mulai sampai selesai
- **Indonesian Date Formatting** - Format tanggal dalam bahasa Indonesia
- **Template Variable Replacement** - Mengganti placeholder dengan data aktual

### 3. 📥 Dual Download System

#### **A. Surat Cuti Download (Tombol Biru)**
- **Individual Download Button** - Muncul di setiap row untuk pengajuan yang sudah disetujui dinas
- **Bulk Download Button** - Download multiple dokumen sekaligus
- **Dashboard Quick Access** - Section khusus "Dokumen Siap Unduh"
- **Template Processing** - Menggunakan template Word dengan variable replacement

#### **B. Lampiran Cuti Download (Tombol Orange)**
- **PDF Attachment Download** - Unduh file PDF yang diupload sebagai lampiran
- **Multiple File Support** - Jika ada beberapa lampiran, tampilkan dropdown
- **Direct Download** - Link langsung ke file yang diupload
- **File Counter** - Badge menampilkan jumlah lampiran

#### **Side-by-Side Layout**
```
[Unduh Surat] [Lampiran (2)]
     ↓              ↓
  Word Doc      PDF Files
```

### 4. 🎯 Smart Access Control

#### **Surat Cuti Download:**
- ✅ **Tersedia** - Hanya untuk pengajuan dengan status `approved_admin`
- ❌ **Tidak Tersedia** - Untuk status lain (pending, approved_coordinator, rejected)
- 🔒 **Visual Indicators** - Icon dan text yang berbeda untuk setiap status

#### **Lampiran Download:**
- ✅ **Selalu Tersedia** - Jika ada file yang diupload
- 📎 **File Counter** - Badge menampilkan jumlah lampiran
- 📄 **File Type Support** - Mendukung PDF dan file lainnya
- ❌ **Tidak Ada Lampiran** - Tampilkan "Tidak ada lampiran"

## 🛠️ Technical Implementation

### Document Generation Process
```typescript
// Surat Cuti Generation
1. Template Selection → getTemplateFilename(leaveType)
2. Data Preparation → generateDocumentData(request)
3. Template Processing → docxtemplater with nullGetter
4. Variable Replacement → Only replace available variables
5. Preserve Original → Keep {nomor_naskah}, {tanggal_naskah}, {ttd_pengirim}
6. File Generation → Blob creation
7. Download Trigger → Automatic download

// Lampiran Download
1. File Check → Verify attachment exists
2. Direct Link → Use original file URL
3. Download Trigger → Browser download
```

### Libraries Used
- **docxtemplater** - Word template processing
- **pizzip** - ZIP file handling for .docx format
- **React Hooks** - State management for download status

### File Naming Convention
```
Surat_Cuti_{NamaLengkap}_{YYYY-MM-DD}.docx
```
Example: `Surat_Cuti_John_Doe_2024-08-12.docx`

## 📊 User Interface Components

### 1. DocumentDownloadButton
**Features:**
- Multiple variants (primary, secondary, outline)
- Size options (sm, md, lg)
- Loading states with spinner
- Success/error feedback
- Tooltip with document type

**Usage:**
```tsx
<DocumentDownloadButton
  request={leaveRequest}
  onSuccess={showSuccessMessage}
  onError={showErrorMessage}
  variant="primary"
  size="md"
/>
```

### 2. CompactDocumentDownloadButton
**Features:**
- Minimal design for table cells
- Icon-only interface
- Hover tooltips
- Loading animation

### 3. BulkDocumentDownloadButton
**Features:**
- Batch processing capability
- Progress tracking
- Counter display
- Error handling for partial failures

### 4. AttachmentDownloadButton
**Features:**
- PDF and file attachment download
- Multiple file support with dropdown
- File counter badge
- Direct download links

### 5. CompactAttachmentDownloadButton
**Features:**
- Minimal design for table cells
- File counter badge
- Paperclip icon
- Hover tooltips

## 🎨 Visual Design

### Button States
- **Idle** - Download icon with "Unduh Dokumen" text
- **Loading** - Spinner with "Mengunduh..." text
- **Success** - Check icon with "Berhasil!" text (3s)
- **Error** - Alert icon with "Gagal" text (5s)

### Color Coding
- 🔵 **Blue** - Surat cuti download buttons
- 🟠 **Orange** - Lampiran download buttons
- 🟢 **Green** - Success states and bulk operations
- 🔴 **Red** - Error states
- ⚪ **Gray** - Disabled/unavailable states

## 📍 Integration Points

### 1. Enhanced Admin Panel
**Dashboard Tab:**
- "Dokumen Siap Unduh" section
- Quick access to recent approved requests
- Bulk download for all approved documents

**Approval Tab:**
- **Dual download buttons** in table rows (Surat + Lampiran)
- **Bulk download button** in toolbar (hanya untuk surat)
- **Status-based visibility** untuk surat cuti
- **Always available** untuk lampiran (jika ada)

### 2. Data Flow
```
LeaveRequest → Status Check → Template Selection → 
Data Processing → Document Generation → Download
```

## 🔧 Configuration

### Template Location
```
public/templates/
├── cuti_tahunan.docx
├── cuti_sakit14.docx
└── cuti_alasanpenting.docx
```

### Template Variables Format
Templates should use `{variable_name}` format for placeholders:
```
Nama: {nama}
NIP: {nip}
Pangkat/Golongan: {pangkat_golongan}
Jabatan: {jabatan}
Unit Kerja: {sekolah}
Lama Cuti: {lama_cuti}
Tanggal Mulai: {tanggal_mulai}
Tanggal Selesai: {tanggal_selesai}
```

## 🚀 Benefits

### For Admin Disdik
✅ **Automated Document Creation** - No manual typing required
✅ **Consistent Formatting** - Professional, standardized documents
✅ **Time Efficiency** - Bulk operations save significant time
✅ **Error Reduction** - Automatic data population prevents mistakes
✅ **Professional Output** - Proper Word format for official use

### For System Efficiency
✅ **Template-based Approach** - Easy to update and maintain
✅ **Scalable Solution** - Handles multiple document types
✅ **Error Handling** - Graceful fallbacks and user feedback
✅ **Performance Optimized** - Efficient processing and download

## 🔍 Usage Examples

### Surat Cuti Download
1. Navigate to Admin Panel → Persetujuan tab
2. Find approved request (status: "Disetujui Dinas")
3. Click **blue download button** (Unduh Dokumen)
4. Word document automatically downloads

### Lampiran Download
1. Navigate to Admin Panel → Persetujuan tab
2. Find any request with attachments
3. Click **orange attachment button** (Lampiran)
4. PDF/attachment files download directly

### Bulk Operations
1. Go to Dashboard or Approval tab
2. Click "Unduh Semua" button (for documents)
3. System processes all approved requests
4. Multiple Word documents download sequentially

### Quick Access Dashboard
1. Check Dashboard → "Dokumen Siap Unduh" section
2. See recent approved requests
3. Use **dual download buttons** (Surat + Lampiran)
4. Or use bulk download for all documents

## 🛡️ Error Handling

### Template Processing Errors
- Fallback to text format if Word processing fails
- Clear error messages for users
- Logging for debugging

### Download Failures
- Retry mechanism for network issues
- User-friendly error messages
- Status indicators for failed downloads

### Browser Compatibility
- Modern browser support for Blob downloads
- Fallback mechanisms for older browsers
- Cross-platform compatibility

The Document Download Feature provides a complete, professional solution for generating and downloading official leave documents, streamlining the administrative process while maintaining high standards of document quality and consistency.
