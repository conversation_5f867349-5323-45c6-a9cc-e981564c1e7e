import React from 'react';
import { FileText, Upload, Users, ExternalLink } from 'lucide-react';

const AboutPage: React.FC = () => {
  const steps = [
    {
      icon: FileText,
      title: 'Isi Form Pengajuan Cuti',
      description: 'Lengkapi semua data yang diperlukan dalam form pengajuan cuti online'
    },
    {
      icon: Upload,
      title: 'Upload Berkas Pendukung',
      description: 'Lampirkan surat permohonan cuti, keterangan dari atasan, dan dokumen pendukung lainnya'
    },
    {
      icon: Users,
      title: 'Proses Persetujuan',
      description: 'Pengajuan akan diproses secara berjenjang mulai dari Koordinator Wilayah hingga Dinas Pendidikan'
    },
    {
      icon: ExternalLink,
      title: 'Pantau Status',
      description: 'Pantau status pengajuan cuti Anda melalui menu Status Pengajuan'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Tata Cara Penggunaan Cuti Online</h1>
          <p className="text-xl text-gray-600 leading-relaxed">
            Panduan lengkap untuk menggunakan sistem pengajuan cuti online ASN Guru 
            Dinas Pendidikan Kabupaten Grobogan.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">{step.title}</h3>
                </div>
                <p className="text-gray-600">{step.description}</p>
              </div>
            );
          })}
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-yellow-900 mb-4">Berkas yang Diperlukan</h2>
          <div className="space-y-3 text-yellow-800">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <span>Surat permohonan cuti yang ditandatangani</span>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <span>Keterangan dari atasan langsung</span>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <span>Lampiran pendukung (surat dokter, undangan, dll) sesuai jenis cuti</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-blue-900 mb-4">Contoh Format Surat</h2>
          <p className="text-blue-800 mb-4">
            Untuk memudahkan Anda, kami menyediakan contoh format surat permohonan cuti dan 
            keterangan dari atasan yang dapat Anda download dan gunakan sebagai referensi.
          </p>
          <a
            href="https://drive.google.com/drive/folders/11F6E9tmqaoRxn1RB4IZZE_1lIZHgEqNK?usp=sharing"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 transition-all duration-200"
          >
            <ExternalLink className="w-5 h-5" />
            <span>Download Contoh Surat</span>
          </a>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-bold text-blue-900 mb-4">Alur Persetujuan</h2>
          <p className="text-blue-800 leading-relaxed">
            Setelah pengajuan disubmit, akan diproses secara berjenjang: 
            <strong>Koordinator Wilayah → Dinas Pendidikan</strong>. 
            Anda dapat memantau status pengajuan melalui menu "Status Pengajuan\" dengan memasukkan NIP Anda.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;