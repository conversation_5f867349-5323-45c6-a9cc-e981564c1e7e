import React from 'react';
import { 
  X, User, Building, Calendar, Clock, FileText, 
  Download, ExternalLink, CheckCircle, AlertCircle, 
  XCircle, Loader, MapPin, GraduationCap, Briefcase,
  Phone, Mail, Eye
} from 'lucide-react';
import { LeaveRequest } from '../types';
import { getAnnualLeaveStats } from '../utils/leaveValidation';

interface LeaveRequestDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  request: LeaveRequest | null;
  existingRequests?: LeaveRequest[];
}

const LeaveRequestDetailModal: React.FC<LeaveRequestDetailModalProps> = ({
  isOpen,
  onClose,
  request,
  existingRequests = []
}) => {
  if (!isOpen || !request) return null;

  // Get annual leave statistics for the user
  const currentYear = new Date().getFullYear();
  const annualLeaveStats = getAnnualLeaveStats(request.nip, currentYear, existingRequests);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'approved_coordinator':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      case 'approved_admin':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Menunggu Persetujuan';
      case 'approved_coordinator':
        return 'Disetujui Koordinator Wilayah';
      case 'approved_admin':
        return 'Disetujui Dinas Pendidikan';
      case 'rejected':
        return 'Ditolak';
      default:
        return 'Status Tidak Diketahui';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved_coordinator':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'approved_admin':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const calculateDuration = () => {
    const start = new Date(request.tanggalMulai);
    const end = new Date(request.tanggalSelesai);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return diffDays;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleFilePreview = (file: any) => {
    if (file.url) {
      window.open(file.url, '_blank');
    }
  };

  const handleFileDownload = (file: any) => {
    if (file.url) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Detail Pengajuan Cuti</h2>
                <p className="text-blue-100">Informasi lengkap pengajuan cuti</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-blue-200 transition-colors p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Left Column - Main Info */}
            <div className="lg:col-span-2 space-y-6">
              
              {/* Personal Information */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <User className="w-5 h-5 mr-2 text-blue-600" />
                  Informasi Pemohon
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Nama Lengkap</label>
                    <p className="text-gray-900 font-medium">{request.nama}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">NIP</label>
                    <p className="text-gray-900 font-mono">{request.nip}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Pangkat/Golongan</label>
                    <p className="text-gray-900">{request.pangkatGolongan}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Jabatan</label>
                    <p className="text-gray-900">{request.jabatan}</p>
                  </div>
                </div>
              </div>

              {/* Work Unit Information */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Building className="w-5 h-5 mr-2 text-green-600" />
                  Unit Kerja
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Sekolah/Unit</label>
                    <p className="text-gray-900">{request.unitKerja}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Kecamatan</label>
                    <p className="text-gray-900 flex items-center">
                      <MapPin className="w-4 h-4 mr-1 text-gray-500" />
                      {request.kecamatan}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Jenjang</label>
                    <p className="text-gray-900 flex items-center">
                      <GraduationCap className="w-4 h-4 mr-1 text-gray-500" />
                      {request.jenjang}
                    </p>
                  </div>
                </div>
              </div>

              {/* Leave Details */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-purple-600" />
                  Detail Cuti
                </h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Jenis Cuti</label>
                      <p className="text-gray-900 font-medium">{request.jenisCuti}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Durasi</label>
                      <p className="text-gray-900 font-medium">{calculateDuration()} Hari</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Tanggal Mulai</label>
                      <p className="text-gray-900">{formatDate(request.tanggalMulai)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Tanggal Selesai</label>
                      <p className="text-gray-900">{formatDate(request.tanggalSelesai)}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Alasan Cuti</label>
                    <p className="text-gray-900 bg-white p-3 rounded border">{request.alasanCuti}</p>
                  </div>
                </div>
              </div>

              {/* Attachments */}
              {request.files && request.files.length > 0 && (
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <FileText className="w-5 h-5 mr-2 text-orange-600" />
                    Lampiran ({request.files.length})
                  </h3>
                  <div className="space-y-3">
                    {request.files.map((file, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                              <FileText className="w-5 h-5 text-orange-600" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{file.name}</p>
                              <p className="text-sm text-gray-500">
                                {file.size ? `${Math.round(file.size / 1024)} KB` : 'File'}
                              </p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                              title="Preview"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleFileDownload(file)}
                              className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
                              title="Download"
                            >
                              <Download className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Status & Actions */}
            <div className="space-y-6">
              
              {/* Status Timeline */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Pengajuan</h3>
                
                <div className={`inline-flex items-center px-3 py-2 rounded-lg border ${getStatusColor(request.status)} mb-4`}>
                  {getStatusIcon(request.status)}
                  <span className="ml-2 font-medium">{getStatusText(request.status)}</span>
                </div>

                <div className="space-y-4">
                  {/* Timeline */}
                  <div className="relative">
                    <div className="absolute left-4 top-8 bottom-0 w-0.5 bg-gray-200"></div>
                    
                    {/* Submitted */}
                    <div className="relative flex items-start space-x-3 pb-4">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <FileText className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Pengajuan Disubmit</p>
                        <p className="text-sm text-gray-500">{formatDate(request.submissionDate)}</p>
                      </div>
                    </div>

                    {/* Coordinator Review */}
                    <div className="relative flex items-start space-x-3 pb-4">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        ['approved_coordinator', 'approved_admin'].includes(request.status)
                          ? 'bg-blue-500'
                          : request.status === 'rejected'
                            ? 'bg-red-500'
                            : 'bg-gray-300'
                      }`}>
                        {['approved_coordinator', 'approved_admin'].includes(request.status) ? (
                          <CheckCircle className="w-4 h-4 text-white" />
                        ) : request.status === 'rejected' ? (
                          <XCircle className="w-4 h-4 text-white" />
                        ) : (
                          <Clock className="w-4 h-4 text-gray-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Review Koordinator Wilayah</p>
                        <p className="text-sm text-gray-500">
                          {['approved_coordinator', 'approved_admin'].includes(request.status)
                            ? `Disetujui${request.coordinatorApprovalDate ? ` - ${formatDate(request.coordinatorApprovalDate)}` : ''}`
                            : request.status === 'rejected'
                              ? 'Ditolak'
                              : 'Menunggu review'}
                        </p>
                      </div>
                    </div>

                    {/* Admin Review */}
                    <div className="relative flex items-start space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        request.status === 'approved_admin'
                          ? 'bg-green-500'
                          : request.status === 'rejected'
                            ? 'bg-red-500'
                            : 'bg-gray-300'
                      }`}>
                        {request.status === 'approved_admin' ? (
                          <CheckCircle className="w-4 h-4 text-white" />
                        ) : request.status === 'rejected' ? (
                          <XCircle className="w-4 h-4 text-white" />
                        ) : (
                          <Clock className="w-4 h-4 text-gray-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Review Dinas Pendidikan</p>
                        <p className="text-sm text-gray-500">
                          {request.status === 'approved_admin'
                            ? `Disetujui${request.adminApprovalDate ? ` - ${formatDate(request.adminApprovalDate)}` : ''}`
                            : request.status === 'rejected'
                              ? 'Ditolak'
                              : 'Menunggu review'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Rejection Reason */}
                {request.status === 'rejected' && request.rejectionReason && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm font-medium text-red-800">Alasan Penolakan:</p>
                    <p className="text-sm text-red-700 mt-1">{request.rejectionReason}</p>
                  </div>
                )}
              </div>

              {/* Annual Leave Quota */}
              {request.jenisCuti === 'Cuti Tahunan' && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Kuota Cuti Tahunan {currentYear}</h3>
                  <div className="space-y-3">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Total Pengajuan</p>
                          <p className="font-semibold text-gray-900">{annualLeaveStats.totalRequests}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Disetujui</p>
                          <p className="font-semibold text-green-600">{annualLeaveStats.approvedRequests}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Pending</p>
                          <p className="font-semibold text-yellow-600">{annualLeaveStats.pendingRequests}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Ditolak</p>
                          <p className="font-semibold text-red-600">{annualLeaveStats.rejectedRequests}</p>
                        </div>
                      </div>
                      <div className="mt-4 pt-4 border-t border-blue-200">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Sisa Kuota:</span>
                          <span className={`font-bold text-lg ${
                            annualLeaveStats.remainingQuota <= 0
                              ? 'text-red-600'
                              : annualLeaveStats.remainingQuota <= 2
                                ? 'text-yellow-600'
                                : 'text-green-600'
                          }`}>
                            {annualLeaveStats.remainingQuota}/12
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div
                            className={`h-2 rounded-full ${
                              annualLeaveStats.remainingQuota <= 0
                                ? 'bg-red-500'
                                : annualLeaveStats.remainingQuota <= 2
                                  ? 'bg-yellow-500'
                                  : 'bg-green-500'
                            }`}
                            style={{
                              width: `${Math.max(0, (annualLeaveStats.remainingQuota / 12) * 100)}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h3>
                <div className="space-y-3">
                  
                  {/* Download Official Letter */}
                  {request.status === 'approved_admin' && request.driveLink && (
                    <a
                      href={request.driveLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full inline-flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Unduh Surat Usulan
                    </a>
                  )}

                  {/* Request ID */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">ID Pengajuan</p>
                    <p className="text-sm font-mono text-gray-900 mt-1">{request.id}</p>
                  </div>

                  {/* Submission Date */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Tanggal Pengajuan</p>
                    <p className="text-sm text-gray-900 mt-1">{formatDate(request.submissionDate)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Tutup
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeaveRequestDetailModal;
