import { createClient } from '@supabase/supabase-js';

// Database utility functions for leave requests and admin users
export interface LeaveRequestDB {
  id: string;
  nama: string;
  nip: string;
  pangkat_golongan: string;
  jabatan: string;
  koordinator_wilayah: string;
  jenjang: string;
  sekolah: string;
  jenis_cuti: string;
  tanggal_mulai: string;
  tanggal_selesai: string;
  alasan_cuti: string;
  files: string; // JSON string
  status: 'pending' | 'approved_coordinator' | 'approved_admin' | 'rejected';
  rejection_reason: string;
  submission_date: string;
  drive_link?: string; // Google Drive link for approved documents
  coordinator_approval_date?: string; // Date when coordinator approved
  admin_approval_date?: string; // Date when admin approved
  created_at: string;
  updated_at: string;
}

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key exists:', !!supabaseAnonKey);

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Export supabase client for use in other modules
export { supabase };

// Test connection
supabase.from('leave_requests').select('count', { count: 'exact', head: true })
  .then(({ count, error }) => {
    if (error) {
      console.error('Supabase connection error:', error);
    } else {
      console.log('Supabase connected successfully. Records count:', count);
    }
  });

// Database operations
export const db = {
  // Get all leave requests
  async getAllLeaveRequests(): Promise<LeaveRequestDB[]> {
    try {
      console.log('Fetching all leave requests from Supabase...');
      const { data, error } = await supabase
        .from('leave_requests')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error in getAllLeaveRequests:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully fetched leave requests:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('Error fetching leave requests:', error);
      throw error;
    }
  },

  // Create new leave request
  async createLeaveRequest(request: Omit<LeaveRequestDB, 'id' | 'created_at' | 'updated_at'>): Promise<LeaveRequestDB | null> {
    try {
      console.log('Creating leave request in Supabase:', request);
      
      // Generate a unique ID
      const id = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const requestWithId = {
        ...request,
        id
      };

      const { data, error } = await supabase
        .from('leave_requests')
        .insert([requestWithId])
        .select()
        .single();

      if (error) {
        console.error('Supabase error in createLeaveRequest:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully created leave request:', data);
      return data;
    } catch (error) {
      console.error('Error creating leave request:', error);
      throw error;
    }
  },

  // Update leave request
  async updateLeaveRequest(id: string, updates: Partial<LeaveRequestDB>): Promise<LeaveRequestDB | null> {
    try {
      console.log('Updating leave request in Supabase:', id, updates);

      // First check if the record exists
      const { data: existingRecord, error: checkError } = await supabase
        .from('leave_requests')
        .select('id')
        .eq('id', id)
        .single();

      if (checkError) {
        console.error('Record not found:', checkError);
        throw new Error(`Record with id ${id} not found`);
      }

      // Now update the record
      const { data, error } = await supabase
        .from('leave_requests')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase error in updateLeaveRequest:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully updated leave request:', data);
      return data;
    } catch (error) {
      console.error('Error updating leave request:', error);
      throw error;
    }
  },

  // Get leave requests by NIP
  async getLeaveRequestsByNIP(nip: string): Promise<LeaveRequestDB[]> {
    try {
      console.log('Fetching leave requests by NIP from Supabase:', nip);
      
      const { data, error } = await supabase
        .from('leave_requests')
        .select('*')
        .eq('nip', nip)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error in getLeaveRequestsByNIP:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully fetched leave requests by NIP:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('Error fetching leave requests by NIP:', error);
      throw error;
    }
  },

  // Delete leave request (if needed)
  async deleteLeaveRequest(id: string): Promise<boolean> {
    try {
      console.log('Deleting leave request from Supabase:', id);
      
      const { error } = await supabase
        .from('leave_requests')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Supabase error in deleteLeaveRequest:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully deleted leave request');
      return true;
    } catch (error) {
      console.error('Error deleting leave request:', error);
      throw error;
    }
  }
};