import React, { useState } from 'react';
import { Upload, X, FileText, AlertCircle, Info } from 'lucide-react';
import { LeaveRequest, FileInfo } from '../types';
import { validateLeaveRequest, getAnnualLeaveStats, validateNIP, validateLeaveDates, isSickLeaveType } from '../utils/leaveValidation';
import { uploadFile, deleteFile, getFilePathFromUrl } from '../utils/storage';
import { smpSkbData, kecamatanList } from '../data/smpData';

interface LeaveFormProps {
  onSubmit: (request: Omit<LeaveRequest, 'id' | 'status' | 'rejectionReason' | 'submissionDate'>) => void;
  showModal: (message: string) => void;
  editingRequest?: LeaveRequest;
  existingRequests?: LeaveRequest[];
}

const LeaveForm: React.FC<LeaveFormProps> = ({ onSubmit, showModal, editingRequest, existingRequests = [] }) => {
  const [formData, setFormData] = useState({
    nama: editingRequest?.nama || '',
    nip: editingRequest?.nip || '',
    pangkatGolongan: editingRequest?.pangkatGolongan || '',
    jabatan: editingRequest?.jabatan || '',
    kecamatan: editingRequest?.kecamatan || '',
    jenjang: editingRequest?.jenjang || '',
    unitKerja: editingRequest?.unitKerja || '',
    jenisCuti: editingRequest?.jenisCuti || '',
    tanggalMulai: editingRequest?.tanggalMulai || '',
    tanggalSelesai: editingRequest?.tanggalSelesai || '',
    alasanCuti: editingRequest?.alasanCuti || '',
  });

  const [files, setFiles] = useState<FileInfo[]>(editingRequest?.files || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationMessage, setValidationMessage] = useState<{ type: 'info' | 'warning' | 'error'; message: string } | null>(null);
  const [nipValidation, setNipValidation] = useState<{ type: 'info' | 'warning' | 'error'; message: string } | null>(null);
  const [dateValidation, setDateValidation] = useState<{ type: 'info' | 'warning' | 'error'; message: string } | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newData = { ...prev, [name]: value };
      // Reset unitKerja when kecamatan or jenjang changes
      if (name === 'kecamatan' || name === 'jenjang') {
        newData.unitKerja = '';
      }

      // Validate NIP when it changes
      if (name === 'nip') {
        const nipValidationResult = value ? validateNIP(value) : { isValid: false, message: 'NIP wajib diisi' };
        if (!nipValidationResult.isValid) {
          setNipValidation({
            type: 'error',
            message: nipValidationResult.message || 'Format NIP tidak valid'
          });
        } else {
          setNipValidation({
            type: 'info',
            message: 'Format NIP valid ✓'
          });
        }
      }

      // Check annual leave stats when NIP or leave type changes
      if ((name === 'nip' || name === 'jenisCuti') && newData.nip && newData.jenisCuti === 'Cuti Tahunan') {
        // Only check quota if NIP is valid
        const nipValidationResult = validateNIP(newData.nip);
        if (nipValidationResult.isValid) {
          const currentYear = new Date().getFullYear();
          const stats = getAnnualLeaveStats(newData.nip, currentYear, existingRequests);

          if (stats.remainingQuota <= 0) {
            setValidationMessage({
              type: 'error',
              message: `Kuota cuti tahunan habis! Anda telah menggunakan ${stats.totalRequests - stats.rejectedRequests}/12 kuota untuk tahun ${currentYear}.`
            });
          } else if (stats.remainingQuota <= 2) {
            setValidationMessage({
              type: 'warning',
              message: `Perhatian: Sisa kuota cuti tahunan ${stats.remainingQuota} kali untuk tahun ${currentYear}.`
            });
          } else {
            setValidationMessage({
              type: 'info',
              message: `Sisa kuota cuti tahunan: ${stats.remainingQuota} kali untuk tahun ${currentYear}.`
            });
          }
        }
      } else if (name === 'jenisCuti' && value !== 'Cuti Tahunan') {
        setValidationMessage(null);
      }

      // Validate dates when tanggalMulai or tanggalSelesai changes
      if ((name === 'tanggalMulai' || name === 'tanggalSelesai') && newData.tanggalMulai && newData.tanggalSelesai) {
        const dateValidationResult = validateLeaveDates(newData.tanggalMulai, newData.tanggalSelesai, newData.jenisCuti);
        if (!dateValidationResult.isValid) {
          setDateValidation({
            type: 'error',
            message: dateValidationResult.message || 'Tanggal tidak valid'
          });
        } else {
          // Show success message for all leave types
          setDateValidation({
            type: 'info',
            message: 'Tanggal cuti valid ✓ (dapat diajukan di masa lalu atau masa depan)'
          });
        }
      } else if (name === 'tanggalMulai' && value) {
        // Validate start date with new policy - allow all leave types for past/future dates
        const today = new Date();
        const startDate = new Date(value);
        today.setHours(0, 0, 0, 0);
        startDate.setHours(0, 0, 0, 0);

        // Check 30-day limit for past dates
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);

        if (startDate < thirtyDaysAgo) {
          setDateValidation({
            type: 'error',
            message: 'Pengajuan cuti tidak dapat diajukan untuk tanggal lebih dari 30 hari yang lalu.'
          });
        } else {
          setDateValidation({
            type: 'info',
            message: 'Tanggal cuti valid ✓ (dapat diajukan di masa lalu atau masa depan)'
          });
        }
      }

      return newData;
    });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    const maxFileSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['application/pdf'];

    // Only allow 1 file
    if (selectedFiles.length > 1) {
      showModal('Hanya dapat mengupload 1 file PDF saja.');
      e.target.value = '';
      return;
    }

    // Check if already has a file
    if (files.length > 0) {
      showModal('Sudah ada file yang diupload. Hapus file yang ada terlebih dahulu untuk mengupload file baru.');
      e.target.value = '';
      return;
    }

    selectedFiles.forEach(file => {
      if (file.size > maxFileSize) {
        showModal(`Ukuran file "${file.name}" (${(file.size / (1024 * 1024)).toFixed(2)} MB) melebihi batas maksimal 5MB.`);
      } else if (!allowedTypes.includes(file.type)) {
        showModal(`Tipe file "${file.name}" tidak didukung. Hanya mendukung file PDF.`);
      } else {
        // Upload file to Supabase storage
        uploadFileToStorage(file);
      }
    });

    // Reset input
    e.target.value = '';
  };

  const uploadFileToStorage = async (file: File) => {
    try {
      showModal(`Mengupload file "${file.name}", mohon tunggu...`);

      const result = await uploadFile(file, 'leave-documents');

      if (result.success && result.url) {
        const fileInfo: FileInfo = {
          name: file.name,
          url: result.url
        };
        setFiles(prev => [...prev, fileInfo]);
        showModal(`✅ File "${file.name}" berhasil diupload ke Supabase storage!`);
      } else {
        showModal(`❌ ${result.error || 'Gagal mengupload file. Silakan coba lagi.'}`);
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      showModal('❌ Terjadi kesalahan saat mengupload file. Silakan coba lagi.');
    }
  };

  const removeFile = (index: number) => {
    const fileToRemove = files[index];

    // Delete from Supabase storage if it's a storage URL
    if (fileToRemove.url.includes('supabase')) {
      const filePath = getFilePathFromUrl(fileToRemove.url);
      if (filePath) {
        deleteFile(filePath).then(success => {
          if (success) {
            console.log('File deleted from storage successfully');
          } else {
            console.error('Failed to delete file from storage');
          }
        });
      }
    }

    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;

    // Validation
    const requiredFields = ['nama', 'nip', 'pangkatGolongan', 'jabatan', 'kecamatan', 'jenjang', 'unitKerja', 'jenisCuti', 'tanggalMulai', 'tanggalSelesai', 'alasanCuti'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData].trim());

    if (missingFields.length > 0) {
      showModal('Mohon lengkapi semua kolom pengajuan cuti yang wajib diisi.');
      return;
    }

    // Comprehensive validation
    const requestData = {
      ...formData,
      files,
      status: 'pending' as const,
      rejectionReason: ''
    };
    const validationResult = validateLeaveRequest(requestData, existingRequests);
    if (!validationResult.isValid) {
      showModal(validationResult.message || 'Pengajuan cuti tidak valid.');
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        ...formData,
        files
      });

      // Reset form only if not editing
      if (!editingRequest) {
        setFormData({
          nama: '',
          nip: '',
          pangkatGolongan: '',
          jabatan: '',
          kecamatan: '',
          jenjang: '',
          unitKerja: '',
          jenisCuti: '',
          tanggalMulai: '',
          tanggalSelesai: '',
          alasanCuti: '',
        });
        setFiles([]);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      showModal('Terjadi kesalahan saat menyimpan pengajuan. Silakan coba lagi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <FileText className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">
            {editingRequest ? 'Edit Pengajuan Cuti' : 'Form Pengajuan Cuti'}
          </h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="nama" className="block text-sm font-semibold text-gray-700 mb-2">
                Nama Lengkap *
              </label>
              <input
                type="text"
                id="nama"
                name="nama"
                value={formData.nama}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Masukkan nama lengkap"
              />
            </div>

            <div>
              <label htmlFor="nip" className="block text-sm font-semibold text-gray-700 mb-2">
                NIP *
              </label>
              <input
                type="text"
                id="nip"
                name="nip"
                value={formData.nip}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Masukkan NIP (18 angka, contoh: 198810052020121006)"
              />

              {/* NIP Validation Message */}
              {nipValidation && (
                <div className={`mt-2 p-3 rounded-lg border flex items-start space-x-2 ${
                  nipValidation.type === 'error'
                    ? 'bg-red-50 border-red-200 text-red-800'
                    : 'bg-green-50 border-green-200 text-green-800'
                }`}>
                  {nipValidation.type === 'error' ? (
                    <AlertCircle className="w-5 h-5 flex-shrink-0 mt-0.5" />
                  ) : (
                    <Info className="w-5 h-5 flex-shrink-0 mt-0.5" />
                  )}
                  <span className="text-sm">{nipValidation.message}</span>
                </div>
              )}
            </div>

            <div>
              <label htmlFor="pangkatGolongan" className="block text-sm font-semibold text-gray-700 mb-2">
                Pangkat / Golongan Ruang *
              </label>
              <input
                type="text"
                id="pangkatGolongan"
                name="pangkatGolongan"
                value={formData.pangkatGolongan}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Contoh: Penata Muda (III/a)"
              />
            </div>

            <div>
              <label htmlFor="jabatan" className="block text-sm font-semibold text-gray-700 mb-2">
                Jabatan *
              </label>
              <input
                type="text"
                id="jabatan"
                name="jabatan"
                value={formData.jabatan}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Contoh: GURU AHLI PERTAMA"
              />
            </div>

            <div>
              <label htmlFor="kecamatan" className="block text-sm font-semibold text-gray-700 mb-2">
                Kecamatan *
              </label>
              <select
                id="kecamatan"
                name="kecamatan"
                value={formData.kecamatan}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              >
                <option value="">Pilih Kecamatan</option>
                {kecamatanList.map((kecamatan) => (
                  <option key={kecamatan} value={kecamatan}>
                    KECAMATAN {kecamatan}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="jenjang" className="block text-sm font-semibold text-gray-700 mb-2">
                Jenjang *
              </label>
              <select
                id="jenjang"
                name="jenjang"
                value={formData.jenjang}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              >
                <option value="">Pilih Jenjang</option>
                <option value="TK">TK</option>
                <option value="SD">SD</option>
                <option value="SMP">SMP & SKB</option>
              </select>
            </div>

            <div>
              <label htmlFor="unitKerja" className="block text-sm font-semibold text-gray-700 mb-2">
                Unit Kerja *
              </label>
              {formData.jenjang === 'SMP' && formData.kecamatan ? (
                <select
                  id="unitKerja"
                  name="unitKerja"
                  value={formData.unitKerja}
                  onChange={handleInputChange}
                  required
                  disabled={isSubmitting}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                >
                  <option value="">Pilih SMP</option>
                  {smpSkbData[formData.kecamatan]?.map((smp) => (
                    <option key={smp} value={smp}>
                      {smp}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type="text"
                  id="unitKerja"
                  name="unitKerja"
                  value={formData.unitKerja}
                  onChange={handleInputChange}
                  required
                  disabled={isSubmitting}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                  placeholder="Contoh: SDN 1 Sugihmanik"
                />
              )}
            </div>

            <div>
              <label htmlFor="jenisCuti" className="block text-sm font-semibold text-gray-700 mb-2">
                Jenis Cuti *
              </label>
              <select
                id="jenisCuti"
                name="jenisCuti"
                value={formData.jenisCuti}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              >
                <option value="">Pilih Jenis Cuti</option>
                <option value="Cuti Tahunan">Cuti Tahunan</option>
                <option value="Cuti Sakit">Cuti Sakit</option>
                <option value="Cuti Alasan Penting">Cuti Alasan Penting</option>
                <option value="Cuti Gol. IV Tahunan">Cuti Gol. IV Tahunan</option>
                <option value="Cuti Melahirkan">Cuti Melahirkan</option>
                <option value="Cuti Umroh">Cuti Umroh</option>
                <option value="Cuti Haji">Cuti Haji</option>
                <option value="Sakit Lebih 14 Hari">Sakit Lebih 14 Hari</option>
              </select>

              {/* General info for all leave types */}
              {formData.jenisCuti && (
                <div className="mt-2 p-3 rounded-lg border border-blue-200 bg-blue-50 text-blue-800">
                  <div className="flex items-start space-x-2">
                    <Info className="w-4 h-4 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="font-medium">Informasi Pengajuan Cuti:</p>
                      <p>• Dapat diajukan untuk tanggal di masa lalu atau masa depan</p>
                      <p>• Batas maksimal: 30 hari ke belakang dari hari ini</p>
                      <p>• Tidak ada batas untuk tanggal masa depan</p>
                      {isSickLeaveType(formData.jenisCuti) && (
                        <p>• <strong>Cuti sakit:</strong> Wajib melampirkan surat keterangan dokter</p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Validation Message */}
              {validationMessage && (
                <div className={`mt-2 p-3 rounded-lg border flex items-start space-x-2 ${
                  validationMessage.type === 'error'
                    ? 'bg-red-50 border-red-200 text-red-800'
                    : validationMessage.type === 'warning'
                    ? 'bg-yellow-50 border-yellow-200 text-yellow-800'
                    : 'bg-blue-50 border-blue-200 text-blue-800'
                }`}>
                  {validationMessage.type === 'error' ? (
                    <AlertCircle className="w-5 h-5 flex-shrink-0 mt-0.5" />
                  ) : (
                    <Info className="w-5 h-5 flex-shrink-0 mt-0.5" />
                  )}
                  <span className="text-sm">{validationMessage.message}</span>
                </div>
              )}
            </div>

            <div>
              <label htmlFor="tanggalMulai" className="block text-sm font-semibold text-gray-700 mb-2">
                Tanggal Mulai Cuti *
              </label>
              <input
                type="date"
                id="tanggalMulai"
                name="tanggalMulai"
                value={formData.tanggalMulai}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              />

              {/* Date Validation Message */}
              {dateValidation && (
                <div className={`mt-2 p-3 rounded-lg border flex items-start space-x-2 ${
                  dateValidation.type === 'error'
                    ? 'bg-red-50 border-red-200 text-red-800'
                    : 'bg-blue-50 border-blue-200 text-blue-800'
                }`}>
                  <AlertCircle className="w-5 h-5 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">{dateValidation.message}</span>
                </div>
              )}
            </div>

            <div>
              <label htmlFor="tanggalSelesai" className="block text-sm font-semibold text-gray-700 mb-2">
                Tanggal Selesai Cuti *
              </label>
              <input
                type="date"
                id="tanggalSelesai"
                name="tanggalSelesai"
                value={formData.tanggalSelesai}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              />
            </div>
          </div>

          <div>
            <label htmlFor="alasanCuti" className="block text-sm font-semibold text-gray-700 mb-2">
              Alasan Cuti *
            </label>
            <textarea
              id="alasanCuti"
              name="alasanCuti"
              value={formData.alasanCuti}
              onChange={handleInputChange}
              required
              disabled={isSubmitting}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-vertical disabled:bg-gray-100"
              placeholder="Jelaskan alasan pengajuan cuti"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Upload Berkas Pendukung
            </label>
            {files.length === 0 ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors duration-200">
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Klik untuk memilih file atau seret file ke sini</p>
                <p className="text-sm text-gray-500">Hanya file PDF, maksimal 1 file dengan ukuran 5MB</p>
                <input
                  type="file"
                  onChange={handleFileUpload}
                  disabled={isSubmitting}
                  className="hidden"
                  id="fileUpload"
                  accept=".pdf,application/pdf"
                />
                <label
                  htmlFor="fileUpload"
                  className={`inline-block mt-4 px-6 py-2 rounded-lg transition-colors duration-200 cursor-pointer ${
                    isSubmitting
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  Pilih File
                </label>
              </div>
            ) : (
              <div className="border-2 border-gray-200 rounded-lg p-4 bg-gray-50">
                <p className="text-sm text-gray-600 mb-2">File sudah diupload (maksimal 1 file):</p>
                <div className="text-xs text-gray-500">Hapus file yang ada untuk mengupload file baru</div>
              </div>
            )}

            {files.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="text-sm font-semibold text-gray-700">File yang diupload:</h4>
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                    <span className="text-sm text-gray-700 truncate flex-1">{file.name}</span>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      disabled={isSubmitting}
                      className="ml-2 p-1 text-red-500 hover:text-red-700 transition-colors duration-200 disabled:text-gray-400"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end pt-6">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-8 py-3 font-semibold rounded-lg transition-all duration-200 shadow-md hover:shadow-lg ${
                isSubmitting
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-green-500 text-white hover:bg-green-600'
              }`}
            >
              {isSubmitting 
                ? 'Menyimpan...' 
                : editingRequest 
                  ? 'Perbarui Pengajuan' 
                  : 'Ajukan Pengajuan'
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LeaveForm;