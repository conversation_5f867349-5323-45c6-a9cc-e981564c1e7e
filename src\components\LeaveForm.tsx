import React, { useState } from 'react';
import { Upload, X, FileText } from 'lucide-react';
import { LeaveRequest, FileInfo } from '../types';
import { uploadFile, deleteFile, getFilePathFromUrl } from '../utils/storage';
import { smpSkbData, kecamatanList } from '../data/smpData';

interface LeaveFormProps {
  onSubmit: (request: Omit<LeaveRequest, 'id' | 'status' | 'rejectionReason' | 'submissionDate'>) => void;
  showModal: (message: string) => void;
  editingRequest?: LeaveRequest;
}

const LeaveForm: React.FC<LeaveFormProps> = ({ onSubmit, showModal, editingRequest }) => {
  const [formData, setFormData] = useState({
    nama: editingRequest?.nama || '',
    nip: editingRequest?.nip || '',
    pangkatGolongan: editingRequest?.pangkatGolongan || '',
    jabatan: editingRequest?.jabatan || '',
    kecamatan: editingRequest?.kecamatan || '',
    jenjang: editingRequest?.jenjang || '',
    unitKerja: editingRequest?.unitKerja || '',
    jenisCuti: editingRequest?.jenisCuti || '',
    tanggalMulai: editingRequest?.tanggalMulai || '',
    tanggalSelesai: editingRequest?.tanggalSelesai || '',
    alasanCuti: editingRequest?.alasanCuti || '',
  });

  const [files, setFiles] = useState<FileInfo[]>(editingRequest?.files || []);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newData = { ...prev, [name]: value };
      // Reset unitKerja when kecamatan or jenjang changes
      if (name === 'kecamatan' || name === 'jenjang') {
        newData.unitKerja = '';
      }
      return newData;
    });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    const maxFileSize = 1 * 1024 * 1024; // 1MB
    const allowedTypes = ['application/pdf'];

    // Only allow 1 file
    if (selectedFiles.length > 1) {
      showModal('Hanya dapat mengupload 1 file PDF saja.');
      e.target.value = '';
      return;
    }

    // Check if already has a file
    if (files.length > 0) {
      showModal('Sudah ada file yang diupload. Hapus file yang ada terlebih dahulu.');
      e.target.value = '';
      return;
    }

    selectedFiles.forEach(file => {
      if (file.size > maxFileSize) {
        showModal(`Ukuran file "${file.name}" (${(file.size / (1024 * 1024)).toFixed(2)} MB) melebihi batas maksimal 1MB.`);
      } else if (!allowedTypes.includes(file.type)) {
        showModal(`Tipe file "${file.name}" tidak didukung. Hanya mendukung file PDF.`);
      } else {
        // Upload file to Supabase storage
        uploadFileToStorage(file);
      }
    });

    // Reset input
    e.target.value = '';
  };

  const uploadFileToStorage = async (file: File) => {
    try {
      showModal(`Mengupload file "${file.name}", mohon tunggu...`);
      
      const result = await uploadFile(file, 'leave-documents');
      
      if (result.success && result.url) {
        const fileInfo: FileInfo = {
          name: file.name,
          url: result.url
        };
        setFiles(prev => [...prev, fileInfo]);
        showModal(`✅ File "${file.name}" berhasil diupload ke Supabase storage!`);
      } else {
        showModal(`❌ ${result.error || 'Gagal mengupload file. Silakan coba lagi.'}`);
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      showModal('❌ Terjadi kesalahan saat mengupload file. Silakan coba lagi.');
    }
  };

  const removeFile = (index: number) => {
    const fileToRemove = files[index];
    
    // Delete from Supabase storage if it's a storage URL
    if (fileToRemove.url.includes('supabase')) {
      const filePath = getFilePathFromUrl(fileToRemove.url);
      if (filePath) {
        deleteFile(filePath).then(success => {
          if (success) {
            console.log('File deleted from storage successfully');
          } else {
            console.error('Failed to delete file from storage');
          }
        });
      }
    }
    
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;

    // Validation
    const requiredFields = ['nama', 'nip', 'pangkatGolongan', 'jabatan', 'kecamatan', 'jenjang', 'unitKerja', 'jenisCuti', 'tanggalMulai', 'tanggalSelesai', 'alasanCuti'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData].trim());

    if (missingFields.length > 0) {
      showModal('Mohon lengkapi semua kolom pengajuan cuti yang wajib diisi.');
      return;
    }

    // Date validation
    if (new Date(formData.tanggalMulai) > new Date(formData.tanggalSelesai)) {
      showModal('Tanggal mulai cuti tidak boleh lebih besar dari tanggal selesai cuti.');
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        ...formData,
        files
      });

      // Reset form only if not editing
      if (!editingRequest) {
        setFormData({
          nama: '',
          nip: '',
          pangkatGolongan: '',
          jabatan: '',
          kecamatan: '',
          jenjang: '',
          unitKerja: '',
          jenisCuti: '',
          tanggalMulai: '',
          tanggalSelesai: '',
          alasanCuti: '',
        });
        setFiles([]);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      showModal('Terjadi kesalahan saat menyimpan pengajuan. Silakan coba lagi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <FileText className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">
            {editingRequest ? 'Edit Pengajuan Cuti' : 'Form Pengajuan Cuti'}
          </h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="nama" className="block text-sm font-semibold text-gray-700 mb-2">
                Nama Lengkap *
              </label>
              <input
                type="text"
                id="nama"
                name="nama"
                value={formData.nama}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Masukkan nama lengkap"
              />
            </div>

            <div>
              <label htmlFor="nip" className="block text-sm font-semibold text-gray-700 mb-2">
                NIP *
              </label>
              <input
                type="text"
                id="nip"
                name="nip"
                value={formData.nip}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Masukkan NIP"
              />
            </div>

            <div>
              <label htmlFor="pangkatGolongan" className="block text-sm font-semibold text-gray-700 mb-2">
                Pangkat / Golongan Ruang *
              </label>
              <input
                type="text"
                id="pangkatGolongan"
                name="pangkatGolongan"
                value={formData.pangkatGolongan}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Contoh: Penata Muda (III/a)"
              />
            </div>

            <div>
              <label htmlFor="jabatan" className="block text-sm font-semibold text-gray-700 mb-2">
                Jabatan *
              </label>
              <input
                type="text"
                id="jabatan"
                name="jabatan"
                value={formData.jabatan}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                placeholder="Contoh: GURU AHLI PERTAMA"
              />
            </div>

            <div>
              <label htmlFor="kecamatan" className="block text-sm font-semibold text-gray-700 mb-2">
                Kecamatan *
              </label>
              <select
                id="kecamatan"
                name="kecamatan"
                value={formData.kecamatan}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              >
                <option value="">Pilih Kecamatan</option>
                {kecamatanList.map((kecamatan) => (
                  <option key={kecamatan} value={kecamatan}>
                    KECAMATAN {kecamatan}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="jenjang" className="block text-sm font-semibold text-gray-700 mb-2">
                Jenjang *
              </label>
              <select
                id="jenjang"
                name="jenjang"
                value={formData.jenjang}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              >
                <option value="">Pilih Jenjang</option>
                <option value="TK">TK</option>
                <option value="SD">SD</option>
                <option value="SMP">SMP & SKB</option>
              </select>
            </div>

            <div>
              <label htmlFor="unitKerja" className="block text-sm font-semibold text-gray-700 mb-2">
                Unit Kerja *
              </label>
              {formData.jenjang === 'SMP' && formData.kecamatan ? (
                <select
                  id="unitKerja"
                  name="unitKerja"
                  value={formData.unitKerja}
                  onChange={handleInputChange}
                  required
                  disabled={isSubmitting}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                >
                  <option value="">Pilih SMP</option>
                  {smpSkbData[formData.kecamatan]?.map((smp) => (
                    <option key={smp} value={smp}>
                      {smp}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type="text"
                  id="unitKerja"
                  name="unitKerja"
                  value={formData.unitKerja}
                  onChange={handleInputChange}
                  required
                  disabled={isSubmitting}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
                  placeholder="Contoh: SDN 1 Sugihmanik"
                />
              )}
            </div>

            <div>
              <label htmlFor="jenisCuti" className="block text-sm font-semibold text-gray-700 mb-2">
                Jenis Cuti *
              </label>
              <select
                id="jenisCuti"
                name="jenisCuti"
                value={formData.jenisCuti}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              >
                <option value="">Pilih Jenis Cuti</option>
                <option value="Cuti Tahunan">Cuti Tahunan</option>
                <option value="Cuti Alasan Penting">Cuti Alasan Penting</option>
                <option value="Cuti Besar">Cuti Besar</option>
                <option value="Cuti Sakit">Cuti Sakit</option>
                <option value="Cuti Melahirkan">Cuti Melahirkan</option>
                <option value="Cuti Ibadah Haji">Cuti Ibadah Haji</option>
              </select>
            </div>

            <div>
              <label htmlFor="tanggalMulai" className="block text-sm font-semibold text-gray-700 mb-2">
                Tanggal Mulai Cuti *
              </label>
              <input
                type="date"
                id="tanggalMulai"
                name="tanggalMulai"
                value={formData.tanggalMulai}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              />
            </div>

            <div>
              <label htmlFor="tanggalSelesai" className="block text-sm font-semibold text-gray-700 mb-2">
                Tanggal Selesai Cuti *
              </label>
              <input
                type="date"
                id="tanggalSelesai"
                name="tanggalSelesai"
                value={formData.tanggalSelesai}
                onChange={handleInputChange}
                required
                disabled={isSubmitting}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100"
              />
            </div>
          </div>

          <div>
            <label htmlFor="alasanCuti" className="block text-sm font-semibold text-gray-700 mb-2">
              Alasan Cuti *
            </label>
            <textarea
              id="alasanCuti"
              name="alasanCuti"
              value={formData.alasanCuti}
              onChange={handleInputChange}
              required
              disabled={isSubmitting}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-vertical disabled:bg-gray-100"
              placeholder="Jelaskan alasan pengajuan cuti"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Upload Berkas Pendukung
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors duration-200">
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Klik untuk memilih file atau seret file ke sini</p>
              <p className="text-sm text-gray-500">Hanya file PDF, maksimal 1 file dengan ukuran 1MB</p>
              <input
                type="file"
                onChange={handleFileUpload}
                disabled={isSubmitting}
                className="hidden"
                id="fileUpload"
                accept=".pdf,application/pdf"
              />
              <label
                htmlFor="fileUpload"
                className={`inline-block mt-4 px-6 py-2 rounded-lg transition-colors duration-200 cursor-pointer ${
                  isSubmitting 
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                Pilih File
              </label>
            </div>

            {files.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="text-sm font-semibold text-gray-700">File yang diupload:</h4>
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                    <span className="text-sm text-gray-700 truncate flex-1">{file.name}</span>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      disabled={isSubmitting}
                      className="ml-2 p-1 text-red-500 hover:text-red-700 transition-colors duration-200 disabled:text-gray-400"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end pt-6">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-8 py-3 font-semibold rounded-lg transition-all duration-200 shadow-md hover:shadow-lg ${
                isSubmitting
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-green-500 text-white hover:bg-green-600'
              }`}
            >
              {isSubmitting 
                ? 'Menyimpan...' 
                : editingRequest 
                  ? 'Perbarui Pengajuan' 
                  : 'Ajukan Pengajuan'
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LeaveForm;