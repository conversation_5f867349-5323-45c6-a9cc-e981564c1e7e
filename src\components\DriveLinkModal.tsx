import React, { useState } from 'react';
import { X, Link, Save, ExternalLink, AlertCircle } from 'lucide-react';
import { LeaveRequest } from '../types';

interface DriveLinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  request: LeaveRequest;
  onSave: (requestId: string, driveLink: string) => Promise<boolean>;
}

const DriveLinkModal: React.FC<DriveLinkModalProps> = ({
  isOpen,
  onClose,
  request,
  onSave
}) => {
  const [driveLink, setDriveLink] = useState(request.driveLink || '');
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');

  if (!isOpen) return null;

  const handleSave = async () => {
    if (!driveLink.trim()) {
      setError('Link Google Drive tidak boleh kosong');
      return;
    }

    // Validate Google Drive URL
    const driveUrlPattern = /^https:\/\/drive\.google\.com\//;
    if (!driveUrlPattern.test(driveLink)) {
      setError('Link harus berupa URL Google Drive yang valid');
      return;
    }

    setIsSaving(true);
    setError('');

    try {
      const success = await onSave(request.id, driveLink);
      if (success) {
        onClose();
      } else {
        setError('Gagal menyimpan link. Silakan coba lagi.');
      }
    } catch (err) {
      console.error('Error saving drive link:', err);
      const errorMessage = err instanceof Error ? err.message : 'Terjadi kesalahan sistem';
      if (errorMessage.includes('multiple (or no) rows returned')) {
        setError('Data tidak ditemukan atau duplikat. Silakan refresh halaman dan coba lagi.');
      } else if (errorMessage.includes('not found')) {
        setError('Data pengajuan tidak ditemukan. Silakan refresh halaman.');
      } else {
        setError(`Kesalahan: ${errorMessage}`);
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestLink = () => {
    if (driveLink) {
      window.open(driveLink, '_blank');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Link className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Link Surat Usulan Cuti</h3>
              <p className="text-sm text-gray-600">Tambahkan link Google Drive</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isSaving}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Request Info */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">Nama:</span>
              <span className="text-sm text-gray-900">{request.nama}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">NIP:</span>
              <span className="text-sm text-gray-900">{request.nip}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">Jenis Cuti:</span>
              <span className="text-sm text-gray-900">{request.jenisCuti}</span>
            </div>
          </div>
        </div>

        {/* Drive Link Input */}
        <div className="space-y-4">
          <div>
            <label htmlFor="driveLink" className="block text-sm font-medium text-gray-700 mb-2">
              Link Google Drive *
            </label>
            <div className="relative">
              <input
                id="driveLink"
                type="url"
                value={driveLink}
                onChange={(e) => {
                  setDriveLink(e.target.value);
                  setError('');
                }}
                placeholder="https://drive.google.com/file/d/..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              />
              {driveLink && (
                <button
                  type="button"
                  onClick={handleTestLink}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-800"
                  title="Test link"
                >
                  <ExternalLink className="w-4 h-4" />
                </button>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Pastikan link dapat diakses oleh pengusul cuti
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Petunjuk:</h4>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Upload surat usulan cuti yang sudah disahkan ke Google Drive</li>
              <li>• Pastikan pengaturan berbagi diset ke "Anyone with the link can view"</li>
              <li>• Copy link dan paste di kolom di atas</li>
              <li>• Pengusul dapat mengunduh melalui menu Status Pengajuan Cuti</li>
            </ul>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            disabled={isSaving}
          >
            Batal
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isSaving || !driveLink.trim()}
          >
            {isSaving ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Menyimpan...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <Save className="w-4 h-4" />
                <span>Simpan Link</span>
              </div>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DriveLinkModal;
