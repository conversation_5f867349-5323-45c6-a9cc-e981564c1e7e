# Simple PowerShell deployment script
Write-Host "=== NETLIFY DEPLOYMENT HELPER ===" -ForegroundColor Green
Write-Host ""

# Check if dist folder exists
if (-not (Test-Path "dist")) {
    Write-Host "Building application..." -ForegroundColor Yellow
    npm run build
}

Write-Host "Build ready: dist folder exists" -ForegroundColor Green
Write-Host ""

# Show what's in dist
Write-Host "Files to deploy:" -ForegroundColor Cyan
Get-ChildItem "dist" | ForEach-Object {
    Write-Host "  - $($_.Name)" -ForegroundColor White
}

Write-Host ""
Write-Host "STEP 1: Opening Netlify..." -ForegroundColor Yellow
Start-Process "https://netlify.com"

Write-Host ""
Write-Host "STEP 2: Opening dist folder..." -ForegroundColor Yellow
Start-Process "explorer.exe" -ArgumentList (Get-Location).Path + "\dist"

Write-Host ""
Write-Host "=== DEPLOYMENT INSTRUCTIONS ===" -ForegroundColor Cyan
Write-Host "1. In Netlify: Sign up or login" -ForegroundColor White
Write-Host "2. Scroll down to 'Deploy without Git'" -ForegroundColor White
Write-Host "3. DRAG the dist folder to the drop zone" -ForegroundColor Yellow
Write-Host "4. Wait for upload to complete" -ForegroundColor White
Write-Host ""
Write-Host "=== ENVIRONMENT VARIABLES ===" -ForegroundColor Cyan
Write-Host "After deployment, add these in Site Settings:" -ForegroundColor White
Write-Host ""
Write-Host "VITE_SUPABASE_URL" -ForegroundColor Green
Write-Host "https://olqzomqxrnzekomszkfe.supabase.co" -ForegroundColor Gray
Write-Host ""
Write-Host "VITE_SUPABASE_ANON_KEY" -ForegroundColor Green
Write-Host "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9scXpvbXF4cm56ZWtvbXN6a2ZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzOTA1NjcsImV4cCI6MjA2Njk2NjU2N30.ZMEar_7pqDhwR3rkNqNIvcDqFvrMOfdo90MT_lAjo1M" -ForegroundColor Gray

Write-Host ""
Write-Host "Ready to deploy! Drag dist folder to Netlify." -ForegroundColor Green
