# 🤱 Cuti Melahirkan - Maksimal 3 Bulan (90 Hari)

## 📋 **Overview**
Implementasi validasi khusus untuk cuti melahirkan yang memungkinkan durasi maksimal 3 bulan (90 hari), berbeda dari jenis cuti lainnya yang maksimal 30 hari.

## 🎯 **Ke<PERSON>jakan Durasi Cuti**

### **📊 Durasi Maksimal per Jenis Cuti:**

| Jen<PERSON> Cuti | Durasi Maksimal | Keterangan |
|------------|-----------------|------------|
| **Cuti Melahirkan** | **90 hari (3 bulan)** | ✅ **Khusus untuk ibu melahirkan** |
| **<PERSON><PERSON>** | 30 hari | Sesuai kuota tahunan |
| **Cuti Sakit** | 30 hari | Dengan surat dokter |
| **Sakit Lebih 14 Hari** | 30 hari | Dengan surat dokter |
| **Cuti Alasan Penting** | 30 hari | <PERSON><PERSON><PERSON> kebijakan |
| **Cuti Haji/Umroh** | 30 hari | Se<PERSON><PERSON> jadwal |
| **Cuti Gol. IV Tahunan** | 30 hari | Sesuai ketentuan |

## 🔧 **Technical Implementation**

### **1. 📝 Enhanced Duration Validation**

#### **File: `src/utils/leaveValidation.ts`**

**Before:**
```typescript
// Simple validation for all leave types
const duration = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
if (duration > 30) {
  return {
    isValid: false,
    message: 'Durasi cuti tidak boleh lebih dari 30 hari.'
  };
}
```

**After:**
```typescript
// Special validation with different rules for maternity leave
const duration = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

if (leaveType === 'Cuti Melahirkan') {
  // Maternity leave: maximum 3 months (90 days)
  if (duration > 90) {
    return {
      isValid: false,
      message: `Cuti melahirkan maksimal 3 bulan (90 hari). Durasi yang dipilih: ${duration} hari.`
    };
  }
} else {
  // Other leave types: maximum 30 days
  if (duration > 30) {
    return {
      isValid: false,
      message: 'Durasi cuti tidak boleh lebih dari 30 hari.'
    };
  }
}
```

### **2. 🎯 Helper Function**

```typescript
// Helper function to check if leave type is maternity leave
export const isMaternityLeave = (leaveType?: string): boolean => {
  return leaveType === 'Cuti Melahirkan';
};
```

### **3. 🎨 Enhanced UI Messages**

#### **Real-time Duration Display:**
```typescript
if (isMaternityLeave(newData.jenisCuti)) {
  const start = new Date(newData.tanggalMulai);
  const end = new Date(newData.tanggalSelesai);
  const duration = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  successMessage = `Tanggal cuti valid ✓ - Durasi: ${duration} hari (maksimal 90 hari untuk cuti melahirkan)`;
}
```

#### **Info Panel Enhancement:**
```typescript
{formData.jenisCuti === 'Cuti Melahirkan' && (
  <p>• <strong>Cuti melahirkan:</strong> Maksimal 3 bulan (90 hari)</p>
)}
```

## 📊 **Validation Rules**

### **🤱 Cuti Melahirkan (Special Rules)**

| Durasi | Status | Pesan |
|--------|--------|-------|
| **1-90 hari** | ✅ Valid | "Tanggal cuti valid ✓ - Durasi: X hari (maksimal 90 hari)" |
| **91+ hari** | ❌ Invalid | "Cuti melahirkan maksimal 3 bulan (90 hari). Durasi yang dipilih: X hari." |

### **📅 Jenis Cuti Lainnya (Standard Rules)**

| Durasi | Status | Pesan |
|--------|--------|-------|
| **1-30 hari** | ✅ Valid | "Tanggal cuti valid ✓" |
| **31+ hari** | ❌ Invalid | "Durasi cuti tidak boleh lebih dari 30 hari." |

## 🎯 **User Experience**

### **1. 📱 Form Interaction untuk Cuti Melahirkan**

```
1. User pilih "Cuti Melahirkan" dari dropdown
2. Info panel muncul: "Cuti melahirkan: Maksimal 3 bulan (90 hari)"
3. User pilih tanggal mulai dan selesai
4. Real-time validation menampilkan durasi:
   - ✅ "Durasi: 45 hari (maksimal 90 hari untuk cuti melahirkan)"
   - ❌ "Cuti melahirkan maksimal 3 bulan (90 hari). Durasi yang dipilih: 120 hari."
```

### **2. 🔄 Dynamic Feedback**

**Valid Duration (≤90 days):**
```
✅ Tanggal cuti valid ✓ - Durasi: 60 hari (maksimal 90 hari untuk cuti melahirkan)
```

**Invalid Duration (>90 days):**
```
❌ Cuti melahirkan maksimal 3 bulan (90 hari). Durasi yang dipilih: 120 hari.
```

### **3. 📋 Info Panel Display**

```
ℹ️ Informasi Pengajuan Cuti:
• Dapat diajukan untuk tanggal di masa lalu atau masa depan
• Batas maksimal: 30 hari ke belakang dari hari ini
• Tidak ada batas untuk tanggal masa depan
• Cuti melahirkan: Maksimal 3 bulan (90 hari)
```

## 🧪 **Testing Scenarios**

### **✅ Valid Maternity Leave Scenarios**
1. **1 bulan (30 hari)** ✅ Valid
2. **2 bulan (60 hari)** ✅ Valid
3. **3 bulan (90 hari)** ✅ Valid
4. **12 minggu (84 hari)** ✅ Valid
5. **Tepat 90 hari** ✅ Valid

### **❌ Invalid Maternity Leave Scenarios**
1. **91 hari** ❌ Invalid
2. **4 bulan (120 hari)** ❌ Invalid
3. **6 bulan (180 hari)** ❌ Invalid

### **✅ Other Leave Types (Unchanged)**
1. **Cuti Tahunan 30 hari** ✅ Valid
2. **Cuti Sakit 30 hari** ✅ Valid
3. **Cuti Tahunan 31 hari** ❌ Invalid

## 📋 **Business Logic**

### **🤱 Why 3 Months for Maternity Leave?**

**Legal Compliance:**
- ✅ **Indonesian Labor Law**: Sesuai dengan UU Ketenagakerjaan
- ✅ **Government Regulation**: Mengikuti peraturan pemerintah
- ✅ **Standard Practice**: Sesuai dengan praktik umum di Indonesia
- ✅ **Health Considerations**: Mempertimbangkan kesehatan ibu dan bayi

**Operational Benefits:**
- ✅ **Adequate Recovery**: Waktu yang cukup untuk pemulihan
- ✅ **Bonding Time**: Waktu untuk bonding dengan bayi
- ✅ **Flexible Planning**: Ibu dapat merencanakan cuti sesuai kebutuhan
- ✅ **Administrative Clarity**: Aturan yang jelas untuk HR

### **🔒 Duration Calculation**

```typescript
// Inclusive calculation (includes both start and end dates)
const duration = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

// Examples:
// Jan 1 - Jan 1 = 1 day
// Jan 1 - Jan 30 = 30 days
// Jan 1 - Mar 31 = 90 days (3 months)
```

## 📊 **Comparison with Other Systems**

### **🏢 Standard Leave Management**

| System | Maternity Leave Duration | Our Implementation |
|--------|-------------------------|-------------------|
| **Government Standard** | 3 months | ✅ 90 days |
| **Private Sector** | 3-6 months | ✅ 90 days (configurable) |
| **International** | 12-52 weeks | ✅ 90 days (12.8 weeks) |
| **Our System** | **90 days** | ✅ **Implemented** |

### **🎯 Flexibility Features**

| Feature | Standard System | Our Implementation |
|---------|----------------|-------------------|
| **Past Dates** | ❌ Usually not allowed | ✅ Allowed (30 days back) |
| **Future Dates** | ✅ Usually allowed | ✅ Unlimited future |
| **Duration Validation** | ✅ Basic | ✅ Real-time with feedback |
| **Type-specific Rules** | ❌ Often generic | ✅ Customized per leave type |

## 🔄 **Implementation Benefits**

### **1. 👥 User Benefits**
- ✅ **Clear Expectations**: User tahu batas durasi yang jelas
- ✅ **Real-time Feedback**: Langsung tahu jika durasi valid/invalid
- ✅ **Flexible Planning**: Dapat merencanakan cuti sesuai kebutuhan
- ✅ **Legal Compliance**: Sesuai dengan hak yang diberikan undang-undang

### **2. 🏢 Administrative Benefits**
- ✅ **Automated Validation**: Sistem otomatis validasi durasi
- ✅ **Consistent Rules**: Aturan yang konsisten untuk semua user
- ✅ **Audit Trail**: Tracking durasi cuti untuk compliance
- ✅ **Reduced Errors**: Mencegah pengajuan cuti yang melebihi batas

### **3. 🔧 Technical Benefits**
- ✅ **Modular Design**: Helper function yang reusable
- ✅ **Type Safety**: TypeScript validation untuk leave types
- ✅ **Maintainable Code**: Easy to modify duration limits
- ✅ **Extensible**: Easy to add new leave types with custom rules

## 🎯 **Future Enhancements**

### **Potential Improvements:**
- 📊 **Configurable Limits**: Admin dapat mengatur durasi maksimal
- 📅 **Calendar Integration**: Visual calendar untuk planning
- 📋 **Documentation Requirements**: Automatic reminder untuk dokumen
- 📈 **Analytics**: Tracking penggunaan cuti melahirkan

---

## ✅ **STATUS: CUTI MELAHIRKAN 3 BULAN DIIMPLEMENTASI**

**Maternity Leave Duration**: ✅ Maksimal 90 hari (3 bulan)
**Other Leave Types**: ✅ Tetap maksimal 30 hari
**Real-time Validation**: ✅ Durasi ditampilkan secara real-time
**User Experience**: ✅ Clear feedback dan informasi yang jelas

**Key Features:**
- 🤱 **Special Rules**: Cuti melahirkan mendapat perlakuan khusus
- 📊 **Duration Display**: Real-time calculation dan display durasi
- 🎯 **Clear Messaging**: Error dan success message yang informatif
- 🔧 **Maintainable Code**: Helper function dan modular design

Sistem sekarang mendukung cuti melahirkan dengan durasi maksimal 3 bulan sesuai dengan ketentuan perundang-undangan yang berlaku!
