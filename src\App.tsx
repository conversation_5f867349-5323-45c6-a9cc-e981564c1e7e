import React, { useState } from 'react';
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import LeaveForm from './components/LeaveForm';
import StatusPage from './components/StatusPage';
import RolePage from './components/RolePage';
import AboutPage from './components/AboutPage';
import Footer from './components/Footer';
import Modal from './components/Modal';
import { LeaveRequest, UserRole } from './types';
import { useLeaveRequests } from './hooks/useLeaveRequests';

type ActiveSection = 'dashboard' | 'form' | 'status' | 'role' | 'about';

function App() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('dashboard');
  const [currentRole, setCurrentRole] = useState<UserRole>('user');
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(false);
  const [modal, setModal] = useState<{ isOpen: boolean; message: string }>({
    isOpen: false,
    message: ''
  });
  const [nipFilter, setNipFilter] = useState('');

  const {
    leaveRequests,
    loading,
    error,
    createLeaveRequest,
    updateLeaveRequest,
    getLeaveRequestsByNIP,
  } = useLeaveRequests();

  const showModal = (message: string) => {
    setModal({ isOpen: true, message });
  };

  const closeModal = () => {
    setModal({ isOpen: false, message: '' });
  };

  const addLeaveRequest = async (request: Omit<LeaveRequest, 'id' | 'status' | 'rejectionReason' | 'submissionDate'>) => {
    const newRequest = await createLeaveRequest(request);
    if (newRequest) {
      showModal('Pengajuan cuti telah berhasil disimpan. Pengajuan Anda akan diproses secara berjenjang.');
      setActiveSection('status');
    } else {
      showModal('Gagal menyimpan pengajuan cuti. Silakan coba lagi.');
    }
  };

  const handleUpdateLeaveRequest = async (id: string, updates: Partial<LeaveRequest>) => {
    const updatedRequest = await updateLeaveRequest(id, updates);
    return updatedRequest !== null;
  };

  const approveRequest = async (id: string, role: 'coordinator' | 'admin') => {
    const request = leaveRequests.find(req => req.id === id);
    if (!request) return;

    const newStatus = role === 'coordinator' ? 'approved_coordinator' : 'approved_admin';
    const success = await handleUpdateLeaveRequest(id, { status: newStatus });
    
    if (success) {
      const roleText = role === 'coordinator' ? 'Koordinator Wilayah' : 'Dinas Pendidikan';
      showModal(`Pengajuan cuti dari ${request.nama} telah disetujui oleh ${roleText}.`);
    } else {
      showModal('Gagal menyetujui pengajuan. Silakan coba lagi.');
    }
  };

  const rejectRequest = async (id: string, role: 'coordinator' | 'admin', reason: string) => {
    const request = leaveRequests.find(req => req.id === id);
    if (!request) return;

    const success = await handleUpdateLeaveRequest(id, { 
      status: 'rejected', 
      rejectionReason: reason 
    });
    
    if (success) {
      const roleText = role === 'coordinator' ? 'Koordinator Wilayah' : 'Admin Dinas';
      showModal(`Pengajuan cuti dari ${request.nama} telah ditolak oleh ${roleText} dengan alasan: "${reason}"`);
    } else {
      showModal('Gagal menolak pengajuan. Silakan coba lagi.');
    }
  };

  const editRejectedRequest = (request: LeaveRequest) => {
    setActiveSection('form');
    // The form component will handle the editing logic
  };

  const renderActiveSection = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-600">Memuat data...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-700">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
          >
            Muat Ulang
          </button>
        </div>
      );
    }

    switch (activeSection) {
      case 'dashboard':
        return <Dashboard leaveRequests={leaveRequests} />;
      case 'form':
        return <LeaveForm onSubmit={addLeaveRequest} showModal={showModal} existingRequests={leaveRequests} />;
      case 'status':
        return (
          <StatusPage 
            leaveRequests={leaveRequests}
            nipFilter={nipFilter}
            setNipFilter={setNipFilter}
            onEditRequest={editRejectedRequest}
            getLeaveRequestsByNIP={getLeaveRequestsByNIP}
          />
        );
      case 'role':
        return (
          <RolePage
            leaveRequests={leaveRequests}
            currentRole={currentRole}
            setCurrentRole={setCurrentRole}
            isAdminLoggedIn={isAdminLoggedIn}
            setIsAdminLoggedIn={setIsAdminLoggedIn}
            onApprove={approveRequest}
            onReject={rejectRequest}
            onUpdate={handleUpdateLeaveRequest}
            showModal={showModal}
          />
        );
      case 'about':
        return <AboutPage />;
      default:
        return <Dashboard leaveRequests={leaveRequests} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header activeSection={activeSection} setActiveSection={setActiveSection} />
      
      <main className="flex-1 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {renderActiveSection()}
        </div>
      </main>

      <Footer />

      <Modal 
        isOpen={modal.isOpen}
        onClose={closeModal}
        message={modal.message}
      />
    </div>
  );
}

export default App;