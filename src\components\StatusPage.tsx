import React, { useState, useEffect } from 'react';
import { Search, RefreshCw, Edit, Eye, Download, ExternalLink } from 'lucide-react';
import LeaveRequestDetailModal from './LeaveRequestDetailModal';
import { LeaveRequest } from '../types';
import StatusBadge from './StatusBadge';

interface StatusPageProps {
  leaveRequests: LeaveRequest[];
  nipFilter: string;
  setNipFilter: (filter: string) => void;
  onEditRequest: (request: LeaveRequest) => void;
  getLeaveRequestsByNIP: (nip: string) => Promise<LeaveRequest[]>;
}

const StatusPage: React.FC<StatusPageProps> = ({ 
  leaveRequests, 
  nipFilter, 
  setNipFilter, 
  onEditRequest,
  getLeaveRequestsByNIP
}) => {
  const [filteredRequests, setFilteredRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [searched, setSearched] = useState(false);
  const [detailModal, setDetailModal] = useState<{
    isOpen: boolean;
    request: LeaveRequest | null;
  }>({ isOpen: false, request: null });

  const handleSearch = async () => {
    if (!nipFilter.trim()) {
      setFilteredRequests([]);
      setSearched(false);
      return;
    }

    setLoading(true);
    try {
      const requests = await getLeaveRequestsByNIP(nipFilter.trim());
      setFilteredRequests(requests);
      setSearched(true);
    } catch (error) {
      console.error('Error searching by NIP:', error);
      setFilteredRequests([]);
      setSearched(true);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setNipFilter('');
    setFilteredRequests([]);
    setSearched(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleShowDetail = (request: LeaveRequest) => {
    setDetailModal({ isOpen: true, request });
  };

  const handleCloseDetail = () => {
    setDetailModal({ isOpen: false, request: null });
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Status Pengajuan Cuti Anda</h1>
        
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <label htmlFor="nipFilter" className="block text-sm font-medium text-gray-700 mb-2">
              Cari berdasarkan NIP:
            </label>
            <input
              type="text"
              id="nipFilter"
              value={nipFilter}
              onChange={(e) => setNipFilter(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Masukkan NIP Anda"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
          </div>
          <div className="flex gap-2 sm:items-end">
            <button
              onClick={handleSearch}
              disabled={loading}
              className="px-6 py-3 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 disabled:bg-blue-300 transition-all duration-200 flex items-center space-x-2"
            >
              <Search className="w-4 h-4" />
              <span>{loading ? 'Mencari...' : 'Cari'}</span>
            </button>
            <button
              onClick={handleClear}
              className="px-6 py-3 bg-gray-500 text-white font-medium rounded-lg hover:bg-gray-600 transition-all duration-200 flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Reset</span>
            </button>
          </div>
        </div>

        {!searched ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 text-lg">
              Silakan masukkan NIP untuk melihat status pengajuan Anda.
            </p>
          </div>
        ) : loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-500 text-lg">Mencari pengajuan...</p>
          </div>
        ) : filteredRequests.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 text-lg">
              Tidak ada pengajuan cuti ditemukan untuk NIP ini.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">No</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Tanggal Pengajuan</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Jenis Cuti</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Tanggal Cuti</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Unit Kerja</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Jenjang</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Kecamatan</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Status</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredRequests.map((request, index) => (
                  <tr key={request.id} className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{index + 1}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">
                      {new Date(request.submissionDate).toLocaleDateString('id-ID')}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.jenisCuti}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">
                      {request.tanggalMulai} s/d {request.tanggalSelesai}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.unitKerja}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.jenjang}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 border-b">{request.kecamatan}</td>
                    <td className="px-6 py-4 border-b">
                      <StatusBadge status={request.status} />
                      {request.status === 'rejected' && request.rejectionReason && (
                        <div className="mt-1 text-xs text-red-600">
                          Alasan: {request.rejectionReason}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 border-b">
                      <div className="flex space-x-2">
                        {request.status === 'rejected' ? (
                          <button
                            onClick={() => onEditRequest(request)}
                            className="inline-flex items-center space-x-1 px-3 py-2 bg-yellow-500 text-white text-sm font-medium rounded-lg hover:bg-yellow-600 transition-all duration-200"
                          >
                            <Edit className="w-4 h-4" />
                            <span>Perbaiki</span>
                          </button>
                        ) : (
                          <button
                            onClick={() => handleShowDetail(request)}
                            className="inline-flex items-center space-x-1 px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded-lg hover:bg-blue-600 transition-all duration-200"
                          >
                            <Eye className="w-4 h-4" />
                            <span>Detail</span>
                          </button>
                        )}

                        {/* Drive Link Download Button */}
                        {request.status === 'approved_admin' && request.driveLink && (
                          <a
                            href={request.driveLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center space-x-1 px-3 py-2 bg-green-500 text-white text-sm font-medium rounded-lg hover:bg-green-600 transition-all duration-200"
                            title="Unduh Surat Usulan Cuti"
                          >
                            <Download className="w-4 h-4" />
                            <span>Unduh Surat</span>
                          </a>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Detail Modal */}
      <LeaveRequestDetailModal
        isOpen={detailModal.isOpen}
        request={detailModal.request}
        onClose={handleCloseDetail}
        existingRequests={leaveRequests}
      />
    </div>
  );
};

export default StatusPage;