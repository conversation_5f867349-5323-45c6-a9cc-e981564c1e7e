# 🏥 Cuti Sakit - Pengajuan Masa Lalu

## 📋 **Overview**
Implementasi fitur khusus untuk cuti sakit yang memungkinkan pengajuan cuti untuk tanggal di masa lalu, mengingat sifat cuti sakit yang sering terjadi mendadak dan perlu diajukan setelah kejadian.

## 🎯 **Business Requirements**
- ✅ **Cuti Sakit** dapat diajukan untuk tanggal di masa lalu
- ✅ **Sakit Lebih 14 Hari** dapat diajukan untuk tanggal di masa lalu
- ✅ **Batas Waktu**: Maksimal 30 hari ke belakang
- ✅ **Jenis Cuti Lain**: Tetap menggunakan validasi normal (tidak boleh masa lalu)
- ✅ **Informasi Jelas**: UI memberikan informasi khusus untuk kedua jenis cuti sakit

## 🔧 **Technical Implementation**

### **1. 📝 Validasi Tanggal yang Dimodifikasi**

#### **File: `src/utils/leaveValidation.ts`**

**Before:**
```typescript
export const validateLeaveDates = (
  startDate: string,
  endDate: string
): ValidationResult => {
  // Always reject past dates
  if (start < today) {
    return {
      isValid: false,
      message: 'Tanggal mulai cuti tidak boleh di masa lalu.'
    };
  }
}
```

**After:**
```typescript
// ✅ Added helper function for consistency
export const isSickLeaveType = (leaveType?: string): boolean => {
  return leaveType === 'Cuti Sakit' || leaveType === 'Sakit Lebih 14 Hari';
};

export const validateLeaveDates = (
  startDate: string,
  endDate: string,
  leaveType?: string  // ✅ Added leave type parameter
): ValidationResult => {
  const isSickLeave = isSickLeaveType(leaveType);

  if (!isSickLeave) {
    // Regular leave validation - no past dates
    if (start < today) {
      return {
        isValid: false,
        message: 'Tanggal mulai cuti tidak boleh di masa lalu.'
      };
    }
  } else {
    // ✅ Sick leave - allow past dates with 30-day limit
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    if (start < thirtyDaysAgo) {
      return {
        isValid: false,
        message: 'Cuti sakit tidak dapat diajukan untuk tanggal lebih dari 30 hari yang lalu.'
      };
    }
  }
}
```

### **2. 🔄 Updated Function Calls**

#### **validateLeaveRequest Function:**
```typescript
// Pass leave type to date validation
const dateValidation = validateLeaveDates(
  newRequest.tanggalMulai, 
  newRequest.tanggalSelesai, 
  newRequest.jenisCuti  // ✅ Pass leave type
);
```

#### **LeaveForm Component:**
```typescript
// Real-time validation with leave type
const dateValidationResult = validateLeaveDates(
  newData.tanggalMulai, 
  newData.tanggalSelesai, 
  newData.jenisCuti  // ✅ Pass current leave type
);
```

### **3. 🎨 Enhanced UI for Sick Leave**

#### **Special Information Panel:**
```typescript
{formData.jenisCuti === 'Cuti Sakit' && (
  <div className="mt-2 p-3 rounded-lg border border-blue-200 bg-blue-50 text-blue-800">
    <div className="flex items-start space-x-2">
      <Info className="w-4 h-4 mt-0.5 flex-shrink-0" />
      <div className="text-sm">
        <p className="font-medium">Informasi Cuti Sakit:</p>
        <p>• Dapat diajukan untuk tanggal di masa lalu (maksimal 30 hari)</p>
        <p>• Wajib melampirkan surat keterangan dokter</p>
      </div>
    </div>
  </div>
)}
```

#### **Enhanced Date Validation Messages:**
```typescript
// Different success messages for sick leave
const successMessage = newData.jenisCuti === 'Cuti Sakit' 
  ? 'Tanggal cuti sakit valid ✓ (dapat diajukan di masa lalu)'
  : 'Tanggal cuti valid ✓';
```

### **4. 📅 Individual Date Validation**

#### **Start Date Validation with Sick Leave Logic:**
```typescript
const isSickLeave = newData.jenisCuti === 'Cuti Sakit';

if (!isSickLeave) {
  // Regular leave - no past dates
  if (startDate < today) {
    setDateValidation({
      type: 'error',
      message: 'Tanggal mulai cuti tidak boleh di masa lalu.'
    });
  }
} else {
  // Sick leave - allow past dates with 30-day limit
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - 30);
  
  if (startDate < thirtyDaysAgo) {
    setDateValidation({
      type: 'error',
      message: 'Cuti sakit tidak dapat diajukan untuk tanggal lebih dari 30 hari yang lalu.'
    });
  } else {
    setDateValidation({
      type: 'info',
      message: 'Tanggal cuti sakit valid ✓ (dapat diajukan di masa lalu)'
    });
  }
}
```

## 📊 **Validation Rules Summary**

### **🏥 Cuti Sakit & Sakit Lebih 14 Hari**
| Condition | Rule | Message |
|-----------|------|---------|
| **Future Dates** | ✅ Allowed | "Tanggal cuti sakit valid ✓" |
| **Today** | ✅ Allowed | "Tanggal cuti sakit valid ✓" |
| **Past 1-30 days** | ✅ Allowed | "Tanggal cuti sakit valid ✓ (dapat diajukan di masa lalu)" |
| **Past >30 days** | ❌ Rejected | "Cuti sakit tidak dapat diajukan untuk tanggal lebih dari 30 hari yang lalu" |

### **📅 Cuti Lainnya (Other Leave Types)**
| Condition | Rule | Message |
|-----------|------|---------|
| **Future Dates** | ✅ Allowed | "Tanggal cuti valid ✓" |
| **Today** | ❌ Rejected | "Pengajuan cuti harus dilakukan sebelum hari cuti dimulai" |
| **Past Dates** | ❌ Rejected | "Tanggal mulai cuti tidak boleh di masa lalu" |

## 🎯 **User Experience**

### **1. 📝 Form Interaction**
1. **Select Leave Type**: User pilih "Cuti Sakit"
2. **Info Panel Appears**: Informasi khusus cuti sakit ditampilkan
3. **Date Selection**: User dapat pilih tanggal masa lalu (max 30 hari)
4. **Real-time Validation**: Pesan validasi yang sesuai ditampilkan
5. **Submit**: Form dapat disubmit dengan tanggal masa lalu

### **2. 🔄 Dynamic Validation**
- **Change Leave Type**: Validasi berubah sesuai jenis cuti
- **Date Input**: Validasi real-time saat input tanggal
- **Visual Feedback**: Warna dan ikon sesuai status validasi

### **3. 📱 Visual Indicators**
- **Blue Info Panel**: Informasi khusus cuti sakit
- **Success Message**: "✓ (dapat diajukan di masa lalu)"
- **Error Message**: Jelas dan spesifik untuk setiap kondisi

## 🧪 **Testing Scenarios**

### **✅ Valid Sick Leave Scenarios**
1. **Future Date**: Cuti sakit untuk besok ✅
2. **Today**: Cuti sakit untuk hari ini ✅
3. **Yesterday**: Cuti sakit untuk kemarin ✅
4. **1 Week Ago**: Cuti sakit 7 hari lalu ✅
5. **30 Days Ago**: Cuti sakit tepat 30 hari lalu ✅

### **❌ Invalid Sick Leave Scenarios**
1. **31 Days Ago**: Cuti sakit 31 hari lalu ❌
2. **60 Days Ago**: Cuti sakit 2 bulan lalu ❌

### **✅ Regular Leave Scenarios**
1. **Future Date**: Cuti tahunan untuk besok ✅
2. **Today**: Cuti tahunan untuk hari ini ❌
3. **Past Date**: Cuti tahunan untuk kemarin ❌

## 📋 **Business Logic**

### **Why 30 Days Limit?**
- **Administrative Reason**: Reasonable time for medical documentation
- **Audit Trail**: Prevents abuse of retroactive leave applications
- **HR Policy**: Aligns with common HR practices
- **System Integrity**: Maintains data consistency

### **Why Only Sick Leave?**
- **Medical Nature**: Illness can be sudden and unpredictable
- **Documentation**: Usually backed by medical certificates
- **Legal Requirement**: Often required by labor laws
- **Practical Need**: Employees may be too sick to apply in advance

## 🔄 **Backward Compatibility**

### **Existing Data**
- ✅ **No Impact**: Existing leave requests remain unchanged
- ✅ **Same Validation**: Non-sick leave types use same rules
- ✅ **Database**: No schema changes required

### **API Compatibility**
- ✅ **Optional Parameter**: `leaveType` is optional in `validateLeaveDates`
- ✅ **Fallback**: Without `leaveType`, uses regular validation
- ✅ **Existing Calls**: Continue to work without modification

---

## ✅ **Implementation Complete**

**Sick Leave Past Dates**: ✅ Implemented with 30-day limit
**UI Enhancement**: ✅ Clear information and validation messages
**Backward Compatibility**: ✅ No breaking changes
**User Experience**: ✅ Intuitive and informative interface

**Key Benefits:**
- 🏥 **Practical**: Addresses real-world sick leave scenarios
- 🎯 **Controlled**: 30-day limit prevents abuse
- 📱 **User-Friendly**: Clear UI guidance and feedback
- 🔒 **Secure**: Maintains validation for other leave types

The system now properly handles sick leave applications for past dates while maintaining strict validation for other leave types!
