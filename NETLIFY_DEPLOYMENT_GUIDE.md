# Panduan Deployment ke Netlify - Sistem Cuti ASN

## 🚀 Overview
Panduan lengkap untuk men-deploy aplikasi Sistem Cuti ASN ke Netlify dengan konfigurasi yang tepat untuk Vite + React + Supabase.

## 📋 Prerequisites

### 1. Akun yang <PERSON>
- ✅ **GitHub Account** - Untuk repository
- ✅ **Netlify Account** - Untuk hosting (gratis)
- ✅ **Supabase Account** - Database sudah ada

### 2. Persiapan Repository
```bash
# Pastikan semua file sudah di-commit
git add .
git commit -m "Prepare for Netlify deployment"
git push origin main
```

## 🔧 Konfigurasi Pre-Deployment

### 1. Buat File Netlify Configuration
Buat file `netlify.toml` di root project:

```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
```

### 2. Environment Variables Setup
Buat file `.env.example` untuk dokumentasi:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Optional: Analytics
VITE_ANALYTICS_ID=your_analytics_id_here
```

### 3. Update .gitignore
Pastikan `.env` ada di `.gitignore`:

```gitignore
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build output
dist/
build/

# Dependencies
node_modules/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
```

## 🌐 Deployment Steps

### Method 1: GitHub Integration (Recommended)

#### Step 1: Push ke GitHub
```bash
# Pastikan repository sudah di GitHub
git remote add origin https://github.com/username/asn-teacher-leave-system.git
git branch -M main
git push -u origin main
```

#### Step 2: Connect ke Netlify
1. Login ke [Netlify](https://netlify.com)
2. Click **"New site from Git"**
3. Choose **GitHub** sebagai provider
4. Select repository **asn-teacher-leave-system**
5. Configure build settings:
   - **Branch to deploy**: `main`
   - **Build command**: `npm run build`
   - **Publish directory**: `dist`

#### Step 3: Environment Variables
Di Netlify Dashboard:
1. Go to **Site settings** → **Environment variables**
2. Add variables:
   ```
   VITE_SUPABASE_URL = https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY = your_anon_key_here
   ```

#### Step 4: Deploy
1. Click **"Deploy site"**
2. Wait for build to complete
3. Site akan tersedia di URL seperti: `https://amazing-name-123456.netlify.app`

### Method 2: Manual Upload

#### Step 1: Build Locally
```bash
# Install dependencies
npm install

# Build for production
npm run build
```

#### Step 2: Upload ke Netlify
1. Go to [Netlify](https://netlify.com)
2. Drag & drop folder `dist` ke Netlify dashboard
3. Site akan otomatis ter-deploy

## ⚙️ Konfigurasi Lanjutan

### 1. Custom Domain (Optional)
1. Di Netlify Dashboard → **Domain settings**
2. Click **"Add custom domain"**
3. Enter domain: `cuti-asn.yourdomain.com`
4. Follow DNS configuration instructions

### 2. HTTPS & Security
- ✅ **HTTPS otomatis** enabled by Netlify
- ✅ **SSL Certificate** otomatis dari Let's Encrypt
- ✅ **Security headers** sudah dikonfigurasi di `netlify.toml`

### 3. Performance Optimization
```toml
# Tambahan di netlify.toml untuk optimasi
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true
```

## 🔒 Security Considerations

### 1. Environment Variables
- ✅ **Never commit** `.env` files
- ✅ **Use Netlify UI** untuk set environment variables
- ✅ **Prefix dengan VITE_** untuk client-side access

### 2. Supabase Security
- ✅ **Row Level Security (RLS)** sudah enabled
- ✅ **API Keys** hanya anon key yang exposed
- ✅ **Database policies** sudah dikonfigurasi

### 3. File Upload Security
- ✅ **File type validation** di frontend
- ✅ **Size limits** dikonfigurasi
- ✅ **Supabase Storage** policies aktif

## 📊 Monitoring & Analytics

### 1. Netlify Analytics
```javascript
// Optional: Add to index.html
<script>
  if (window.netlifyIdentity) {
    window.netlifyIdentity.on("init", user => {
      if (!user) {
        window.netlifyIdentity.on("login", () => {
          document.location.href = "/admin/";
        });
      }
    });
  }
</script>
```

### 2. Performance Monitoring
- ✅ **Lighthouse scores** via Netlify
- ✅ **Core Web Vitals** monitoring
- ✅ **Build time** tracking

## 🚨 Troubleshooting

### Common Issues & Solutions

#### 1. Build Fails
```bash
# Check Node version
node --version  # Should be 18+

# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm run build
```

#### 2. Environment Variables Not Working
- ✅ Pastikan prefix `VITE_`
- ✅ Restart build setelah add env vars
- ✅ Check case sensitivity

#### 3. Routing Issues (404 on refresh)
- ✅ Pastikan `netlify.toml` ada redirects
- ✅ Check SPA routing configuration

#### 4. Supabase Connection Issues
```javascript
// Debug connection
console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('Supabase Key:', import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 10) + '...');
```

## 📱 Post-Deployment Checklist

### ✅ Functionality Tests
- [ ] Login system works
- [ ] File upload works
- [ ] Document download works
- [ ] Database operations work
- [ ] All admin panels accessible

### ✅ Performance Tests
- [ ] Page load speed < 3s
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility

### ✅ Security Tests
- [ ] HTTPS enabled
- [ ] Environment variables secure
- [ ] No sensitive data in console

## 🔄 Continuous Deployment

### Auto-Deploy Setup
1. **GitHub Integration** sudah enable auto-deploy
2. **Branch Protection** untuk production
3. **Preview Deploys** untuk pull requests

### Build Hooks
```bash
# Webhook URL dari Netlify untuk trigger builds
curl -X POST -d {} https://api.netlify.com/build_hooks/your_hook_id
```

## 📞 Support & Resources

### Netlify Resources
- 📖 [Netlify Docs](https://docs.netlify.com/)
- 💬 [Netlify Community](https://community.netlify.com/)
- 🎓 [Netlify University](https://university.netlify.com/)

### Supabase Resources
- 📖 [Supabase Docs](https://supabase.com/docs)
- 💬 [Supabase Discord](https://discord.supabase.com/)

---

## 🎉 Deployment Success!

Setelah mengikuti panduan ini, aplikasi Sistem Cuti ASN akan tersedia secara online dengan:
- ✅ **HTTPS Security**
- ✅ **Auto-deployment** dari GitHub
- ✅ **Global CDN** untuk performa optimal
- ✅ **Monitoring & Analytics**
- ✅ **Professional URL**

**Sample URL**: `https://sistem-cuti-asn.netlify.app`
