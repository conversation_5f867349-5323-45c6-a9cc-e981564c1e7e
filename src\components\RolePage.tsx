import React, { useState } from 'react';
import { Users, Shield, UserCheck, Lock, X } from 'lucide-react';
import { LeaveRequest, UserRole } from '../types';
import CoordinatorPanel from './CoordinatorPanel';
import AdminPanel from './AdminPanel';
import DataRecapPanel from './DataRecapPanel';

interface RolePageProps {
  leaveRequests: LeaveRequest[];
  currentRole: UserRole;
  setCurrentRole: (role: UserRole) => void;
  isAdminLoggedIn: boolean;
  setIsAdminLoggedIn: (loggedIn: boolean) => void;
  onApprove: (id: string, role: 'coordinator' | 'admin') => void;
  onReject: (id: string, role: 'coordinator' | 'admin', reason: string) => void;
  showModal: (message: string) => void;
}

const RolePage: React.FC<RolePageProps> = ({
  leaveRequests,
  currentRole,
  setCurrentRole,
  isAdminLoggedIn,
  setIsAdminLoggedIn,
  onApprove,
  onReject,
  showModal
}) => {
  const [activeTab, setActiveTab] = useState<'approval' | 'recap'>('approval');

  const adminTypes = [
    {
      type: 'admin' as const,
      label: 'Admin Dinas Pendidikan',
      icon: Shield,
      className: 'bg-purple-500 hover:bg-purple-600',
      description: 'Persetujuan Final & Rekap Data'
    },
    {
      type: 'coordinator' as const,
      label: 'Admin Korwil & SMP',
      icon: UserCheck,
      className: 'bg-orange-500 hover:bg-orange-600',
      description: 'Persetujuan Jenjang Pertama'
    }
  ];

  const handleAdminClick = (type: 'admin' | 'coordinator') => {
    setCurrentRole(type);
    setIsAdminLoggedIn(true);
    showModal(`Login berhasil sebagai ${type === 'admin' ? 'Admin Dinas Pendidikan' : 'Admin Korwil & SMP'}`);
  };

  const handleLogout = () => {
    setIsAdminLoggedIn(false);
    setCurrentRole('user');
    setActiveTab('approval');
    showModal('Logout berhasil');
  };

  const renderRolePanel = () => {
    switch (currentRole) {
      case 'admin':
        return (
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="bg-white rounded-lg border border-gray-200 p-1">
              <div className="flex space-x-1">
                <button
                  onClick={() => setActiveTab('approval')}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                    activeTab === 'approval'
                      ? 'bg-purple-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Persetujuan Cuti
                </button>
                <button
                  onClick={() => setActiveTab('recap')}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                    activeTab === 'recap'
                      ? 'bg-purple-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Rekap Data Cuti
                </button>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'approval' ? (
              <AdminPanel
                leaveRequests={leaveRequests}
                onApprove={onApprove}
                onReject={onReject}
                showModal={showModal}
              />
            ) : (
              <DataRecapPanel leaveRequests={leaveRequests} />
            )}
          </div>
        );
      case 'coordinator':
        return (
          <CoordinatorPanel
            leaveRequests={leaveRequests}
            onApprove={onApprove}
            onReject={onReject}
            showModal={showModal}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        {!isAdminLoggedIn ? (
          <>
            <div className="flex items-center space-x-3 mb-6">
              <Users className="w-8 h-8 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900">Pilih Jenis Admin</h1>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">Hierarki Persetujuan Cuti</h3>
              <div className="space-y-2 text-blue-800">
                <p><strong>Jenjang 1:</strong> Admin Korwil & SMP (TK, SD, SMP)</p>
                <p><strong>Jenjang 2:</strong> Admin Dinas Pendidikan (Final Approval)</p>
              </div>
            </div>
            
            <p className="text-gray-600 mb-6">
              Silakan pilih jenis admin untuk mengakses panel administrasi.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {adminTypes.map((admin, index) => {
                const Icon = admin.icon;
                return (
                  <button
                    key={index}
                    onClick={() => handleAdminClick(admin.type)}
                    className={`flex flex-col items-center space-y-4 p-8 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl ${admin.className}`}
                  >
                    <Icon className="w-12 h-12" />
                    <span className="text-xl text-center">{admin.label}</span>
                    <span className="text-sm text-center opacity-90">{admin.description}</span>
                  </button>
                );
              })}
            </div>
          </>
        ) : (
          <>
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-3xl font-bold text-gray-900">
                Panel Admin {currentRole === 'admin' ? 'Dinas Pendidikan' : 'Korwil & SMP'}
              </h1>
              <button
                onClick={handleLogout}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all duration-200"
              >
                Logout
              </button>
            </div>
            {renderRolePanel()}
          </>
        )}
      </div>
    </div>
  );
};

export default RolePage;