import React, { useState, useEffect } from 'react';
import { Users, Shield, UserCheck, Lock, X, LogOut } from 'lucide-react';
import { LeaveRequest, UserRole } from '../types';
import CoordinatorPanel from './CoordinatorPanel';
import AdminPanel from './AdminPanel';
import DataRecapPanel from './DataRecapPanel';
import EnhancedAdminPanel from './EnhancedAdminPanel';
import AdminLogin from './AdminLogin';
import { useAdminAuth } from '../hooks/useAdminAuth';

interface RolePageProps {
  leaveRequests: LeaveRequest[];
  currentRole: UserRole;
  setCurrentRole: (role: UserRole) => void;
  isAdminLoggedIn: boolean;
  setIsAdminLoggedIn: (loggedIn: boolean) => void;
  onApprove: (id: string, role: 'coordinator' | 'admin') => void;
  onReject: (id: string, role: 'coordinator' | 'admin', reason: string) => void;
  onUpdate: (id: string, updates: Partial<LeaveRequest>) => Promise<boolean>;
  showModal: (message: string) => void;
}

const RolePage: React.FC<RolePageProps> = ({
  leaveRequests,
  currentRole,
  setCurrentRole,
  isAdminLoggedIn,
  setIsAdminLoggedIn,
  onApprove,
  onReject,
  onUpdate,
  showModal
}) => {
  const [activeTab, setActiveTab] = useState<'approval' | 'recap'>('approval');
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [selectedAdminType, setSelectedAdminType] = useState<'admin' | 'coordinator' | null>(null);

  // Use the new admin authentication hook
  const {
    isAuthenticated,
    user,
    login,
    logout,
    loading,
    error,
    getFilteredLeaveRequests,
    getRoleDisplayName,
    getPermissionsSummary,
    canApproveAtLevel
  } = useAdminAuth();

  const adminTypes = [
    {
      type: 'admin' as const,
      label: 'Admin Dinas Pendidikan',
      icon: Shield,
      className: 'bg-purple-500 hover:bg-purple-600',
      description: 'Persetujuan Final & Rekap Data'
    },
    {
      type: 'coordinator' as const,
      label: 'Admin Korwil & SMP',
      icon: UserCheck,
      className: 'bg-orange-500 hover:bg-orange-600',
      description: 'Persetujuan Jenjang Pertama'
    }
  ];

  const handleAdminClick = (type: 'admin' | 'coordinator') => {
    setSelectedAdminType(type);
    setShowLoginModal(true);
  };

  const handleLoginSuccess = (loggedInUser: any) => {
    setShowLoginModal(false);
    setSelectedAdminType(null);

    // Set the appropriate role based on user type
    if (loggedInUser.role === 'admin_disdik') {
      setCurrentRole('admin');
    } else {
      setCurrentRole('coordinator');
    }

    setIsAdminLoggedIn(true);
    showModal(`Login berhasil sebagai ${getRoleDisplayName(loggedInUser.role)}`);
  };

  const handleLogout = () => {
    logout();
    setIsAdminLoggedIn(false);
    setCurrentRole('user');
    setActiveTab('approval');
    showModal('Logout berhasil');
  };

  // Sync role after refresh based on authenticated user
  useEffect(() => {
    if (isAuthenticated && user) {
      // Map backend role to UI role
      if (user.role === 'admin_disdik') {
        setCurrentRole('admin');
      } else {
        setCurrentRole('coordinator');
      }
    }
  }, [isAuthenticated, user, setCurrentRole]);

  const handleLoginCancel = () => {
    setShowLoginModal(false);
    setSelectedAdminType(null);
  };

  // Get filtered leave requests based on user permissions
  const filteredLeaveRequests = isAuthenticated && user ? getFilteredLeaveRequests(leaveRequests) : leaveRequests;

  const renderRolePanel = () => {
    if (!isAuthenticated || !user) return null;

    console.log('RolePage Debug:', { currentRole, userRole: user.role, isAuthenticated, user });

    switch (currentRole) {
      case 'admin':
      case 'admin_disdik':
        return (
          <div className="space-y-6">
            {/* User Info Banner */}
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-purple-900">{user.nama}</h3>
                  <p className="text-purple-700">{getRoleDisplayName()}</p>
                  <p className="text-sm text-purple-600">{getPermissionsSummary()}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-purple-600">Username: {user.username}</p>
                </div>
              </div>
            </div>



            {/* Enhanced Admin Panel */}
            <EnhancedAdminPanel
              leaveRequests={filteredLeaveRequests}
              onApprove={onApprove}
              onReject={onReject}
              onUpdate={onUpdate}
              showModal={showModal}
              userRole={user?.role}
              userPermissions={user?.permissions}
            />
          </div>
        );
      case 'coordinator':
        return (
          <div className="space-y-6">
            {/* User Info Banner */}
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-orange-900">{user.nama}</h3>
                  <p className="text-orange-700">{getRoleDisplayName()}</p>
                  <p className="text-sm text-orange-600">{getPermissionsSummary()}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-orange-600">Username: {user.username}</p>
                </div>
              </div>
            </div>

            <CoordinatorPanel
              leaveRequests={filteredLeaveRequests}
              onApprove={onApprove}
              onReject={onReject}
              showModal={showModal}
            />
          </div>
        );
      default:
        return (
          <div className="text-center py-8">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">Role Tidak Dikenali</h3>
              <p className="text-yellow-700 mb-4">
                Role "{currentRole}" tidak dikenali dalam sistem.
              </p>
              <p className="text-sm text-yellow-600">
                Silakan hubungi administrator sistem untuk bantuan.
              </p>
              <button
                onClick={handleLogout}
                className="mt-4 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
              >
                Logout dan Coba Lagi
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        {!isAuthenticated ? (
          <>
            <div className="flex items-center space-x-3 mb-6">
              <Users className="w-8 h-8 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900">Pilih Jenis Admin</h1>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">Hierarki Persetujuan Cuti</h3>
              <div className="space-y-2 text-blue-800">
                <p><strong>Jenjang 1:</strong> Admin Korwil & SMP (TK, SD, SMP)</p>
                <p><strong>Jenjang 2:</strong> Admin Dinas Pendidikan (Final Approval)</p>
              </div>
            </div>

            <p className="text-gray-600 mb-6">
              Silakan pilih jenis admin untuk mengakses panel administrasi.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {adminTypes.map((admin, index) => {
                const Icon = admin.icon;
                return (
                  <button
                    key={index}
                    onClick={() => handleAdminClick(admin.type)}
                    className={`flex flex-col items-center space-y-4 p-8 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl ${admin.className}`}
                  >
                    <Icon className="w-12 h-12" />
                    <span className="text-xl text-center">{admin.label}</span>
                    <span className="text-sm text-center opacity-90">{admin.description}</span>
                  </button>
                );
              })}
            </div>
          </>
        ) : (
          <>
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Panel Admin {user ? getRoleDisplayName() : 'Administrator'}
                </h1>
                {user && (
                  <p className="text-gray-600 mt-1">
                    Selamat datang, {user.nama}
                  </p>
                )}
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all duration-200"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
            {renderRolePanel()}
          </>
        )}
      </div>

      {/* Login Modal */}
      {showLoginModal && (
        <AdminLogin
          onLogin={async (credentials) => {
            const result = await login(credentials);
            if (result.success && result.user) {
              handleLoginSuccess(result.user);
            }
            return result;
          }}
          onCancel={handleLoginCancel}
          loading={loading}
        />
      )}
    </div>
  );
};

export default RolePage;