# PowerShell script to start deployment process
# Usage: .\start-deployment.ps1

Write-Host "Starting Netlify Deployment Process..." -ForegroundColor Green
Write-Host ""

# Check if dist folder exists
if (-not (Test-Path "dist")) {
    Write-Host "dist folder not found. Running build first..." -ForegroundColor Red
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed. Please fix errors and try again." -ForegroundColor Red
        exit 1
    }
}

Write-Host "Build folder ready: dist/" -ForegroundColor Green

# Show folder contents
Write-Host ""
Write-Host "Files ready for deployment:" -ForegroundColor Cyan
Get-ChildItem -Path "dist" -Recurse | ForEach-Object {
    $relativePath = $_.FullName.Replace((Get-Location).Path + "\dist\", "")
    if ($_.PSIsContainer) {
        Write-Host "  FOLDER: $relativePath/" -ForegroundColor Yellow
    } else {
        $size = [math]::Round($_.Length / 1KB, 1)
        Write-Host "  FILE: $relativePath ($size KB)" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Opening Netlify in browser..." -ForegroundColor Blue

# Open Netlify in default browser
Start-Process "https://netlify.com"

Write-Host ""
Write-Host "DEPLOYMENT INSTRUCTIONS:" -ForegroundColor Cyan
Write-Host "1. Sign up/Login to Netlify" -ForegroundColor White
Write-Host "2. Scroll down to 'Deploy without Git'" -ForegroundColor White
Write-Host "3. DRAG the 'dist' folder to the drop zone" -ForegroundColor Yellow
Write-Host "4. Wait for upload to complete" -ForegroundColor White
Write-Host ""
Write-Host "🔧 ENVIRONMENT VARIABLES TO ADD:" -ForegroundColor Cyan
Write-Host "After deployment, go to Site Settings > Environment Variables:" -ForegroundColor White
Write-Host ""
Write-Host "VITE_SUPABASE_URL" -ForegroundColor Green
Write-Host "https://olqzomqxrnzekomszkfe.supabase.co" -ForegroundColor Gray
Write-Host ""
Write-Host "VITE_SUPABASE_ANON_KEY" -ForegroundColor Green
Write-Host "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9scXpvbXF4cm56ZWtvbXN6a2ZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzOTA1NjcsImV4cCI6MjA2Njk2NjU2N30.ZMEar_7pqDhwR3rkNqNIvcDqFvrMOfdo90MT_lAjo1M" -ForegroundColor Gray

Write-Host ""
Write-Host "⏱️  Total deployment time: ~5 minutes" -ForegroundColor Green
Write-Host "🎯 Expected result: Fully functional web app" -ForegroundColor Green
Write-Host ""

# Open file explorer to dist folder
Write-Host "📂 Opening dist folder in File Explorer..." -ForegroundColor Blue
Start-Process "explorer.exe" -ArgumentList (Get-Location).Path + "\dist"

Write-Host ""
Write-Host "🚀 Ready to deploy! Drag the dist folder to Netlify." -ForegroundColor Green
Write-Host "📖 For detailed steps, see: DEPLOYMENT_STEPS.md" -ForegroundColor Cyan

# Wait for user input
Write-Host ""
Read-Host "Press Enter after successful deployment to continue"

Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host "📱 Test your app on mobile and desktop" -ForegroundColor Cyan
Write-Host "🔗 Share the URL with your team" -ForegroundColor Cyan
Write-Host "📊 Monitor usage in Netlify dashboard" -ForegroundColor Cyan
