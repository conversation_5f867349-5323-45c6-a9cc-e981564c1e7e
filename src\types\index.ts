export interface LeaveRequest {
  id: string;
  nama: string;
  nip: string;
  pangkatGolongan: string;
  jabatan: string;
  kecamatan: string;
  jenjang: string;
  unitKerja: string;
  jenisCuti: string;
  tanggalMulai: string;
  tanggalSelesai: string;
  alasanCuti: string;
  files: FileInfo[];
  status: 'pending' | 'approved_coordinator' | 'approved_admin' | 'rejected';
  rejectionReason: string;
  submissionDate: string;
}

export interface FileInfo {
  name: string;
  url: string;
}

export type UserRole = 'user' | 'coordinator' | 'admin';

export interface DashboardStats {
  new: number;
  pending: number;
  approvedCoordinator: number;
  approvedAdmin: number;
}