export interface LeaveRequest {
  id: string;
  nama: string;
  nip: string;
  pangkatGolongan: string;
  jabatan: string;
  kecamatan: string;
  jenjang: string;
  unitKerja: string;
  jenisCuti: string;
  tanggalMulai: string;
  tanggalSelesai: string;
  alasanCuti: string;
  files: FileInfo[];
  status: 'pending' | 'approved_coordinator' | 'approved_admin' | 'rejected';
  rejectionReason: string;
  submissionDate: string;
  driveLink?: string; // Google Drive link for approved documents
  coordinatorApprovalDate?: string; // Date when coordinator approved
  adminApprovalDate?: string; // Date when admin approved
}

export interface FileInfo {
  name: string;
  url: string;
}

export type UserRole = 'user' | 'coordinator' | 'admin';

export interface DashboardStats {
  new: number;
  pending: number;
  approvedCoordinator: number;
  approvedAdmin: number;
}

// Enhanced user management types
export interface AdminUser {
  id: string;
  nama: string;
  username: string;
  role: 'admin_disdik' | 'korwil' | 'smp_admin';
  permissions: AdminPermissions;
  created_at: string;
  updated_at: string;
}

export interface AdminPermissions {
  // For admin_disdik - can access all
  canAccessAll?: boolean;

  // For korwil - specific kecamatan access
  kecamatanAccess?: string[];
  jenjangAccess?: ('TK' | 'SD')[];

  // For smp_admin - specific school access
  schoolAccess?: string[];
  jenjangAccess?: ('SMP' | 'SKB')[];
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: AdminUser | null;
  token: string | null;
}