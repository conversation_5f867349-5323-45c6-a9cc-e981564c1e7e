# PowerShell Deployment script for Netlify
# Usage: .\deploy.ps1

Write-Host "🚀 Starting deployment preparation..." -ForegroundColor Green

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js 18+ first." -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  .env file not found. Creating from .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "📝 Please edit .env file with your Supabase credentials before deploying." -ForegroundColor Cyan
        Write-Host "   VITE_SUPABASE_URL=https://your-project-id.supabase.co" -ForegroundColor Cyan
        Write-Host "   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key" -ForegroundColor Cyan
        Read-Host "Press Enter after updating .env file"
    } else {
        Write-Host "❌ .env.example not found. Please create .env file manually." -ForegroundColor Red
        exit 1
    }
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Blue
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

# Run build
Write-Host "🔨 Building application..." -ForegroundColor Blue
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build successful!" -ForegroundColor Green

# Check if dist folder exists
if (-not (Test-Path "dist")) {
    Write-Host "❌ dist folder not found after build" -ForegroundColor Red
    exit 1
}

Write-Host "📁 Build output ready in 'dist' folder" -ForegroundColor Green

# Git operations
Write-Host "📝 Preparing Git commit..." -ForegroundColor Blue

# Add all files
git add .

# Check if there are changes to commit
$gitStatus = git status --porcelain
if (-not $gitStatus) {
    Write-Host "ℹ️  No changes to commit" -ForegroundColor Yellow
} else {
    Write-Host "💾 Committing changes..." -ForegroundColor Blue
    $commitMessage = "Prepare for deployment - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    git commit -m $commitMessage
    
    # Push to main branch
    Write-Host "⬆️  Pushing to GitHub..." -ForegroundColor Blue
    git push origin main
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Successfully pushed to GitHub" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Failed to push to GitHub. You may need to push manually." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Deployment preparation complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Go to https://netlify.com" -ForegroundColor White
Write-Host "2. Click 'New site from Git'" -ForegroundColor White
Write-Host "3. Connect your GitHub repository" -ForegroundColor White
Write-Host "4. Set build command: npm run build" -ForegroundColor White
Write-Host "5. Set publish directory: dist" -ForegroundColor White
Write-Host "6. Add environment variables in Netlify dashboard:" -ForegroundColor White
Write-Host "   - VITE_SUPABASE_URL" -ForegroundColor Yellow
Write-Host "   - VITE_SUPABASE_ANON_KEY" -ForegroundColor Yellow
Write-Host "7. Deploy!" -ForegroundColor White
Write-Host ""
Write-Host "Or drag and drop the 'dist' folder to Netlify for manual deployment." -ForegroundColor Cyan
